Archive member included to satisfy reference by file (symbol)

esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                              (esp_bootloader_desc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                              (__assert_func)
esp-idf/main/libmain.a(bootloader_start.c.obj)
                              (call_start_cpu0)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_utility_load_partition_table)
esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_partition_table_verify)
esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_load_image)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_console_deinit)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_sha256_start)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_ana_clock_glitch_reset_config)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_init)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_common_ota_select_crc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (bootloader_clock_configure)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (bootloader_init_mem)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj) (bootloader_fill_random)
esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_flash_encryption_enabled)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_random_disable)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_mmap_get_free_pages)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (bootloader_flash_update_id)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (bootloader_clear_bss_section)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (bootloader_console_init)
esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (ESP_EFUSE_DIS_DIRECT_BOOT)
esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_enable_rom_secure_download_mode)
esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_read_field_blob)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj) (esp_efuse_utility_process)
esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_get_key_dis_read)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (esp_efuse_utility_clear_program_registers)
esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj) (esp_cpu_configure_region_protection)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj) (rtc_clk_init)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj) (rtc_clk_32k_enable)
esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj) (esp_rom_install_uart_printf)
esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj) (esp_rom_output_set_as_console)
esp-idf/log/liblog.a(log_noos.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (esp_log_timestamp)
esp-idf/soc/libsoc.a(uart_periph.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj) (uart_periph_signal)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (efuse_hal_chip_revision)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/hal/libhal.a(efuse_hal.c.obj) (efuse_hal_get_major_chip_version)
esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (wdt_hal_init)
esp-idf/hal/libhal.a(mmu_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (mmu_hal_unmap_all)
esp-idf/hal/libhal.a(cache_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj) (cache_hal_init)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__lshrdi3)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__ashldi3)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__popcountsi2)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (__divdi3)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (__udivdi3)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                              E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o) (__clz_tab)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj) (memcmp)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (_impure_data)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (memset)
E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (memcpy)

Discarded input sections

 .text          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj
 .data          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj
 .bss           0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj
 .comment       0x00000000       0x30 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj
 .riscv.attributes
                0x00000000       0x44 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj
 .text          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .text          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .data          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .bss           0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text.__getreent
                0x00000000        0xa esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_get_partition_description
                0x00000000       0x9e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_atexit
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_hex_to_str
                0x00000000       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_flash_contents
                0x00000000       0xba esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image_no_verify
                0x00000000        0xe esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_metadata
                0x00000000       0xc2 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader_data
                0x00000000       0x26 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_flash_size
                0x00000000       0x52 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_write_protect_crypt_cnt
                0x00000000       0x10 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_get_flash_encryption_mode
                0x00000000       0x8e esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_set_release_mode.str1.4
                0x00000000       0xd9 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_set_release_mode
                0x00000000      0x124 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_cfg_verify_release_mode.str1.4
                0x00000000      0x3ea esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_cfg_verify_release_mode
                0x00000000      0x2ea esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_flash_erase_range
                0x00000000       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_spi_flash_reset
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.7       0x00000000       0x88 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.8       0x00000000        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .text.bootloader_flash_update_size
                0x00000000        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .iram1.1       0x00000000       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .iram1.3       0x00000000       0x1a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SYS_DATA_PART2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA_MAC_CUSTOM
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_OCODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_TEMP_CALIB
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MINOR_HI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_THRES_HVT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIG_DBIAS_HVT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_V_DIG_DBIAS20
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_V_RTC_DBIAS20
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_K_DIG_LDO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_K_RTC_LDO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_VENDOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TEMP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_CAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MINOR_LO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_D7
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_D6
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_D5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_D4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_DQS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_WP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_HD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_CS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_D
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_Q
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_PAD_CONFIG_CLK
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data.ESP_EFUSE_MAC
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ERR_RST_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_VDD_SPI_AS_GPIO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USB_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SOFT_DIS_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_JTAG_SEL_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_TWAI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_VDD_SPI_AS_GPIO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_USB_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CUSTOM_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_USR_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_OCODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_TEMP_CALIB
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SYS_DATA_PART1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR_HI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_THRES_HVT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIG_DBIAS_HVT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_V_DIG_DBIAS20
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_V_RTC_DBIAS20
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_K_DIG_LDO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_K_RTC_LDO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_VENDOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TEMP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_CAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR_LO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D7
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D6
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_DQS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_WP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_HD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_CS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_Q
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_CLK
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ERR_RST_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_TWAI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SYS_DATA_PART2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY5  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY4  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY3  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY2  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY1  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA_MAC_CUSTOM
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.OCODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.TEMP_CALIB
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.OPTIONAL_UNIQUE_ID
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MINOR_HI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.THRES_HVT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIG_DBIAS_HVT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.V_DIG_DBIAS20
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.V_RTC_DBIAS20
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.K_DIG_LDO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.K_RTC_LDO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_VENDOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TEMP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_CAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MINOR_LO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_D7
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_D6
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_D5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_D4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_DQS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_WP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_HD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_CS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_D
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_Q
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_PAD_CONFIG_CLK
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .rodata.MAC    0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ERR_RST_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.VDD_SPI_AS_GPIO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USB_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SOFT_DIS_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.JTAG_SEL_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_TWAI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_FORCE_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_VDD_SPI_AS_GPIO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_USB_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CUSTOM_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_USR_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_OCODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_TEMP_CALIB
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SYS_DATA_PART1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MINOR_HI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_THRES_HVT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIG_DBIAS_HVT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_V_DIG_DBIAS20
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_V_RTC_DBIAS20
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_K_DIG_LDO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_K_RTC_LDO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_VENDOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TEMP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_CAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MINOR_LO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_D7
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_D6
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_D5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_D4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_DQS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_WP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_HD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_CS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_D
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_Q
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_PAD_CONFIG_CLK
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ERR_RST_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_TWAI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_info    0x00000000     0x24cd esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_abbrev  0x00000000      0x106 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_aranges
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_line    0x00000000      0x1ef esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_str     0x00000000     0x26b6 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_get_pkg_ver
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_set_rom_log_scheme
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_disable_rom_download_mode
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_enable_rom_secure_download_mode
                0x00000000       0x34 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_info    0x00000000      0x3ba esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_abbrev  0x00000000      0x18f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_loc     0x00000000       0x1f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_aranges
                0x00000000       0x38 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_ranges  0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_line    0x00000000      0x450 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_str     0x00000000      0x5ee esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_frame   0x00000000       0x80 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_blob
                0x00000000       0x6e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_read_field_bit.str1.4
                0x00000000       0x3b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_bit
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_cnt
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_blob
                0x00000000       0x74 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_write_field_cnt.str1.4
                0x00000000       0x59 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_cnt
                0x00000000       0xac esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_bit
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_get_field_size
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_reg
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_reg
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_begin.str1.4
                0x00000000       0x5c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_begin
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_cancel.str1.4
                0x00000000       0x76 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_cancel
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_commit.str1.4
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_commit
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_check_errors
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_destroy_block.str1.4
                0x00000000      0x14b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_destroy_block
                0x00000000      0x13c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.0
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.1
                0x00000000       0x13 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.2
                0x00000000       0x19 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .sbss.s_batch_writing_mode
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_info    0x00000000     0x11af esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_abbrev  0x00000000      0x3cb esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_loc     0x00000000      0xa5b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_aranges
                0x00000000       0x98 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_ranges  0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_line    0x00000000     0x115a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_str     0x00000000      0x8f9 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_frame   0x00000000      0x220 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.write_reg.str1.4
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.write_reg
                0x00000000       0x7a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_process.str1.4
                0x00000000       0x69 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_process
                0x00000000      0x18a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_reset
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_efuses
                0x00000000       0x2e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_erase_virt_blocks
                0x00000000        0x2 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_update_virt_blocks.str1.4
                0x00000000       0x32 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_update_virt_blocks
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_single_block.str1.4
                0x00000000       0x12 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_single_block
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_pending
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_blocks.str1.4
                0x00000000        0xd esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_blocks
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_number_of_items
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_read_reg
                0x00000000       0x68 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_fill_buff
                0x00000000       0xde esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_count_once
                0x00000000       0x62 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_cnt.str1.4
                0x00000000       0x31 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_cnt
                0x00000000       0xce esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_reg.str1.4
                0x00000000       0x5e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_reg
                0x00000000       0x64 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_blob
                0x00000000       0x92 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_get_read_register_address.str1.4
                0x00000000       0x16 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_read_register_address
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_is_correct_written_data.str1.4
                0x00000000       0xdd esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_is_correct_written_data
                0x00000000       0xd6 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.0
                0x00000000       0x2c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.1
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.2
                0x00000000        0xa esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.3
                0x00000000        0xf esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.4
                0x00000000       0x1a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .sbss.s_burn_counter
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000     0x14af esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x437 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000     0x145d esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0xa8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x2c8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000     0x17ad esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0xa44 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_block_is_empty
                0x00000000       0x36 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_read_protect
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_coding_scheme
                0x00000000        0x6 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_purpose_field
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_key_dis_read.str1.4
                0x00000000       0x8f esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_read
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_read
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_write
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_write
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_purpose
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_purpose
                0x00000000       0x3c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_keypurpose_dis_write
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_keypurpose_dis_write
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_purpose
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_key_block_unused
                0x00000000       0x5e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_unused_key_block
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_count_unused_key_blocks
                0x00000000       0x32 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_write_key.str1.4
                0x00000000       0x70 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_key
                0x00000000      0x116 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_write_keys.str1.4
                0x00000000       0xed esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_keys
                0x00000000      0x156 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_digest_revoke.str1.4
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_digest_revoke
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_digest_revoke
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_write_protect_of_digest_revoke
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect_of_digest_revoke
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_secure_boot_read_key_digests.str1.4
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_secure_boot_read_key_digests
                0x00000000       0xa8 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.0
                0x00000000       0x21 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.1
                0x00000000       0x2d esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.2
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.3
                0x00000000       0x23 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.4
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.5
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_revoke_table
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_table
                0x00000000       0x78 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_info    0x00000000     0x1594 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_abbrev  0x00000000      0x3b8 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_loc     0x00000000      0xdce esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_aranges
                0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_ranges  0x00000000      0x210 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_line    0x00000000     0x1448 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_str     0x00000000     0x1036 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_frame   0x00000000      0x324 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_clear_program_registers
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_check_errors.str1.4
                0x00000000       0x84 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_check_errors
                0x00000000       0x7c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_burn_chip_opt.str1.4
                0x00000000      0x203 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip_opt
                0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip
                0x00000000        0xc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_apply_new_coding_scheme.str1.4
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_apply_new_coding_scheme
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_write_addr_blocks
                0x00000000       0x58 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss.write_mass_blocks
                0x00000000      0x160 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_read_addr_blocks
                0x00000000       0x58 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000     0x3e75 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x367 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000      0x3f1 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0x40 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x190 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000      0xcb8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000     0x2bd2 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000       0xe8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_bbpll_add_consumer
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_bbpll_remove_consumer
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable_external
                0x00000000       0x2c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_bootstrap
                0x00000000        0x8 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enabled
                0x00000000       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8m_enabled
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8md256_enabled
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_set_to_default_config
                0x00000000       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_xtal
                0x00000000       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_config_fast
                0x00000000       0x36 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_enable
                0x00000000       0x16 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_disable
                0x00000000       0x16 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_8m_enabled
                0x00000000        0xc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .iram1.0       0x00000000       0x22 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .iram1.1       0x00000000       0x1c esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .text          0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .data          0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .bss           0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.esp_log_impl_lock.str1.4
                0x00000000       0x2c esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_lock
                0x00000000       0x38 esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_lock_timeout
                0x00000000       0x14 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.esp_log_impl_unlock.str1.4
                0x00000000        0xc esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_unlock
                0x00000000       0x3a esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.__func__.1
                0x00000000       0x14 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.__func__.0
                0x00000000       0x12 esp-idf/log/liblog.a(log_noos.c.obj)
 .sbss.s_lock   0x00000000        0x4 esp-idf/log/liblog.a(log_noos.c.obj)
 .text          0x00000000        0x0 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .data          0x00000000        0x0 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .bss           0x00000000        0x0 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_get_mac
                0x00000000       0x14 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_set_timing
                0x00000000       0x44 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_read
                0x00000000       0x4c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_clear_program_registers
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_program
                0x00000000       0x6a esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_rs_calculate
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_is_coding_error_in_block
                0x00000000       0x6e esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .text.wdt_hal_deinit
                0x00000000       0x70 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .text.wdt_hal_disable
                0x00000000       0x20 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .text.wdt_hal_handle_intr
                0x00000000       0x2c esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .text.wdt_hal_feed
                0x00000000       0x1c esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .text.wdt_hal_is_enabled
                0x00000000       0x12 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_pages_to_bytes
                0x00000000        0x6 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_bytes_to_pages
                0x00000000        0x6 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_paddr_to_vaddr
                0x00000000       0x64 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_region
                0x00000000       0x64 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_vaddr_to_paddr
                0x00000000       0x68 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_vaddr_to_cache_level_id.part.0
                0x00000000       0x4c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_suspend
                0x00000000       0x1a esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_resume
                0x00000000       0x24 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_is_cache_enabled
                0x00000000        0xa esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_vaddr_to_cache_level_id
                0x00000000       0x10 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_invalidate_addr
                0x00000000       0x38 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_get_cache_line_size
                0x00000000       0x22 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text          0x00000000       0x28 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_info    0x00000000      0x1af E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_abbrev  0x00000000      0x10c E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_loclists
                0x00000000       0x6b E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line    0x00000000      0x107 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_str     0x00000000      0x1dd E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line_str
                0x00000000      0x210 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_frame   0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .text          0x00000000       0x28 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_info    0x00000000      0x1af E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_abbrev  0x00000000      0x10c E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_loclists
                0x00000000       0x6b E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line    0x00000000      0x107 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_str     0x00000000      0x1dd E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line_str
                0x00000000      0x210 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_frame   0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .text          0x00000000       0x42 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_info    0x00000000       0xdf E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_abbrev  0x00000000       0x65 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_loclists
                0x00000000       0xd6 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line    0x00000000       0xe9 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_str     0x00000000      0x1a4 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line_str
                0x00000000      0x210 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_frame   0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .text          0x00000000      0x39e E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_info    0x00000000      0x7a1 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_abbrev  0x00000000      0x1bf E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_loclists
                0x00000000      0x5ab E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_rnglists
                0x00000000       0x93 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_line    0x00000000      0x9ae E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_str     0x00000000      0x252 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_line_str
                0x00000000      0x210 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .eh_frame      0x00000000       0x28 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .text          0x00000000      0x35e E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_info    0x00000000      0x76f E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x00000000      0x1a6 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_loclists
                0x00000000      0x6ff E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_rnglists
                0x00000000       0x99 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line    0x00000000      0x905 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_str     0x00000000      0x253 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line_str
                0x00000000      0x210 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .eh_frame      0x00000000       0x28 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .text          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .rodata        0x00000000      0x100 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_info    0x00000000       0xe6 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_abbrev  0x00000000       0x70 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_aranges
                0x00000000       0x18 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line    0x00000000       0x3f E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_str     0x00000000      0x1a0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line_str
                0x00000000      0x210 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .text          0x00000000       0x4a E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_info    0x00000000      0x10f E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_abbrev  0x00000000       0x8a E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_loclists
                0x00000000      0x130 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line    0x00000000      0x15f E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_str     0x00000000      0x112 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line_str
                0x00000000      0x2e9 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_frame   0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .text          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .data          0x00000000       0xf0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .sdata         0x00000000        0x4 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_info    0x00000000      0x84c E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_abbrev  0x00000000      0x174 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_aranges
                0x00000000       0x18 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line    0x00000000       0x51 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_str     0x00000000      0x4e2 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line_str
                0x00000000      0x2f1 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .text          0x00000000       0xa8 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line    0x00000000      0x18e E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line_str
                0x00000000      0x10b E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_info    0x00000000       0x33 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_abbrev  0x00000000       0x28 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_str     0x00000000      0x11e E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .riscv.attributes
                0x00000000       0x42 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .text          0x00000000       0xe8 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .data          0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .bss           0x00000000        0x0 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_info    0x00000000      0x256 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_abbrev  0x00000000      0x107 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_loclists
                0x00000000      0x1d6 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_aranges
                0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line    0x00000000      0x31c E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_str     0x00000000      0x133 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line_str
                0x00000000      0x3b6 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .comment       0x00000000       0x30 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_frame   0x00000000       0x20 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .riscv.attributes
                0x00000000       0x44 E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)

Memory Configuration

Name             Origin             Length             Attributes
iram_seg         0x403cc710         0x00002000         xrw
iram_loader_seg  0x403ce710         0x00007000         xrw
dram_seg         0x3fcd5710         0x00005000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

                0x00000000                        IDF_TARGET_ESP32C3 = 0x0
LOAD CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/main/libmain.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libnosys.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a
START GROUP
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libnosys.a
END GROUP
                [!provide]                        PROVIDE (esprv_int_set_priority = esprv_intc_int_set_priority)
                [!provide]                        PROVIDE (esprv_int_set_threshold = esprv_intc_int_set_threshold)
                [!provide]                        PROVIDE (esprv_int_enable = esprv_intc_int_enable)
                [!provide]                        PROVIDE (esprv_int_disable = esprv_intc_int_disable)
                [!provide]                        PROVIDE (esprv_int_set_type = esprv_intc_int_set_type)
                0x40000018                        rtc_get_reset_reason = 0x40000018
                0x4000001c                        analog_super_wdt_reset_happened = 0x4000001c
                0x40000020                        jtag_cpu_reset_happened = 0x40000020
                0x40000024                        rtc_get_wakeup_cause = 0x40000024
                0x40000028                        rtc_boot_control = 0x40000028
                0x4000002c                        rtc_select_apb_bridge = 0x4000002c
                0x40000030                        rtc_unhold_all_pads = 0x40000030
                0x40000034                        set_rtc_memory_crc = 0x40000034
                0x40000038                        cacl_rtc_memory_crc = 0x40000038
                0x4000003c                        ets_is_print_boot = 0x4000003c
                0x40000040                        ets_printf = 0x40000040
                0x40000044                        ets_install_putc1 = 0x40000044
                0x40000048                        ets_install_uart_printf = 0x40000048
                0x4000004c                        ets_install_putc2 = 0x4000004c
                0x40000050                        PROVIDE (ets_delay_us = 0x40000050)
                0x40000054                        ets_get_stack_info = 0x40000054
                0x40000058                        ets_install_lock = 0x40000058
                0x4000005c                        ets_backup_dma_copy = 0x4000005c
                0x40000060                        ets_apb_backup_init_lock_func = 0x40000060
                0x40000064                        UartRxString = 0x40000064
                0x40000068                        uart_tx_one_char = 0x40000068
                0x4000006c                        uart_tx_one_char2 = 0x4000006c
                0x40000070                        uart_rx_one_char = 0x40000070
                0x40000074                        uart_rx_one_char_block = 0x40000074
                0x40000078                        uart_rx_readbuff = 0x40000078
                0x4000007c                        uartAttach = 0x4000007c
                0x40000080                        uart_tx_flush = 0x40000080
                0x40000084                        uart_tx_wait_idle = 0x40000084
                0x40000088                        uart_div_modify = 0x40000088
                0x4000008c                        multofup = 0x4000008c
                0x40000090                        software_reset = 0x40000090
                0x40000094                        software_reset_cpu = 0x40000094
                0x40000098                        assist_debug_clock_enable = 0x40000098
                0x4000009c                        assist_debug_record_enable = 0x4000009c
                0x400000a0                        clear_super_wdt_reset_flag = 0x400000a0
                0x400000a4                        disable_default_watchdog = 0x400000a4
                0x400000a8                        send_packet = 0x400000a8
                0x400000ac                        recv_packet = 0x400000ac
                0x400000b0                        GetUartDevice = 0x400000b0
                0x400000b4                        UartDwnLdProc = 0x400000b4
                0x400000b8                        Uart_Init = 0x400000b8
                0x400000bc                        ets_set_user_start = 0x400000bc
                0x3ff1fffc                        ets_rom_layout_p = 0x3ff1fffc
                0x3fcdfffc                        ets_ops_table_ptr = 0x3fcdfffc
                0x400000c0                        mz_adler32 = 0x400000c0
                0x400000c4                        mz_crc32 = 0x400000c4
                0x400000c8                        mz_free = 0x400000c8
                0x400000cc                        tdefl_compress = 0x400000cc
                0x400000d0                        tdefl_compress_buffer = 0x400000d0
                0x400000d4                        tdefl_compress_mem_to_heap = 0x400000d4
                0x400000d8                        tdefl_compress_mem_to_mem = 0x400000d8
                0x400000dc                        tdefl_compress_mem_to_output = 0x400000dc
                0x400000e0                        tdefl_get_adler32 = 0x400000e0
                0x400000e4                        tdefl_get_prev_return_status = 0x400000e4
                0x400000e8                        tdefl_init = 0x400000e8
                0x400000ec                        tdefl_write_image_to_png_file_in_memory = 0x400000ec
                0x400000f0                        tdefl_write_image_to_png_file_in_memory_ex = 0x400000f0
                0x400000f4                        tinfl_decompress = 0x400000f4
                0x400000f8                        tinfl_decompress_mem_to_callback = 0x400000f8
                0x400000fc                        tinfl_decompress_mem_to_heap = 0x400000fc
                0x40000100                        tinfl_decompress_mem_to_mem = 0x40000100
                [!provide]                        PROVIDE (jd_prepare = 0x40000104)
                [!provide]                        PROVIDE (jd_decomp = 0x40000108)
                0x4000010c                        PROVIDE (esp_rom_spiflash_wait_idle = 0x4000010c)
                0x40000110                        PROVIDE (esp_rom_spiflash_write_encrypted = 0x40000110)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_encrypted_dest = 0x40000114)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_encrypted_enable = 0x40000118)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_encrypted_disable = 0x4000011c)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_chip = 0x40000120)
                0x40000124                        PROVIDE (esp_rom_spiflash_erase_block = 0x40000124)
                0x40000128                        PROVIDE (esp_rom_spiflash_erase_sector = 0x40000128)
                0x4000012c                        PROVIDE (esp_rom_spiflash_write = 0x4000012c)
                0x40000130                        PROVIDE (esp_rom_spiflash_read = 0x40000130)
                0x40000134                        PROVIDE (esp_rom_spiflash_config_param = 0x40000134)
                [!provide]                        PROVIDE (esp_rom_spiflash_read_user_cmd = 0x40000138)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_qio_pins = 0x4000013c)
                [!provide]                        PROVIDE (esp_rom_spiflash_unlock = 0x40000140)
                [!provide]                        PROVIDE (esp_rom_spi_flash_auto_sus_res = 0x40000144)
                [!provide]                        PROVIDE (esp_rom_spi_flash_send_resume = 0x40000148)
                [!provide]                        PROVIDE (esp_rom_spi_flash_update_id = 0x4000014c)
                0x40000150                        PROVIDE (esp_rom_spiflash_config_clk = 0x40000150)
                [!provide]                        PROVIDE (esp_rom_spiflash_config_readmode = 0x40000154)
                [!provide]                        PROVIDE (esp_rom_spiflash_read_status = 0x40000158)
                [!provide]                        PROVIDE (esp_rom_spiflash_read_statushigh = 0x4000015c)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_status = 0x40000160)
                [!provide]                        PROVIDE (esp_rom_spiflash_attach = 0x40000164)
                [!provide]                        PROVIDE (spi_flash_get_chip_size = 0x40000168)
                [!provide]                        PROVIDE (spi_flash_guard_set = 0x4000016c)
                [!provide]                        PROVIDE (spi_flash_guard_get = 0x40000170)
                [!provide]                        PROVIDE (spi_flash_write_config_set = 0x40000174)
                [!provide]                        PROVIDE (spi_flash_write_config_get = 0x40000178)
                [!provide]                        PROVIDE (spi_flash_safe_write_address_func_set = 0x4000017c)
                [!provide]                        PROVIDE (spi_flash_unlock = 0x40000180)
                [!provide]                        PROVIDE (spi_flash_erase_range = 0x40000184)
                [!provide]                        PROVIDE (spi_flash_erase_sector = 0x40000188)
                [!provide]                        PROVIDE (spi_flash_write = 0x4000018c)
                [!provide]                        PROVIDE (spi_flash_read = 0x40000190)
                [!provide]                        PROVIDE (spi_flash_write_encrypted = 0x40000194)
                [!provide]                        PROVIDE (spi_flash_read_encrypted = 0x40000198)
                [!provide]                        PROVIDE (spi_flash_mmap_os_func_set = 0x4000019c)
                [!provide]                        PROVIDE (spi_flash_mmap_page_num_init = 0x400001a0)
                [!provide]                        PROVIDE (spi_flash_mmap = 0x400001a4)
                [!provide]                        PROVIDE (spi_flash_mmap_pages = 0x400001a8)
                [!provide]                        PROVIDE (spi_flash_munmap = 0x400001ac)
                [!provide]                        PROVIDE (spi_flash_mmap_dump = 0x400001b0)
                [!provide]                        PROVIDE (spi_flash_check_and_flush_cache = 0x400001b4)
                [!provide]                        PROVIDE (spi_flash_mmap_get_free_pages = 0x400001b8)
                [!provide]                        PROVIDE (spi_flash_cache2phys = 0x400001bc)
                [!provide]                        PROVIDE (spi_flash_phys2cache = 0x400001c0)
                [!provide]                        PROVIDE (spi_flash_disable_cache = 0x400001c4)
                [!provide]                        PROVIDE (spi_flash_restore_cache = 0x400001c8)
                [!provide]                        PROVIDE (spi_flash_cache_enabled = 0x400001cc)
                [!provide]                        PROVIDE (spi_flash_enable_cache = 0x400001d0)
                [!provide]                        PROVIDE (spi_cache_mode_switch = 0x400001d4)
                [!provide]                        PROVIDE (spi_common_set_dummy_output = 0x400001d8)
                [!provide]                        PROVIDE (spi_common_set_flash_cs_timing = 0x400001dc)
                [!provide]                        PROVIDE (esp_enable_cache_flash_wrap = 0x400001e0)
                [!provide]                        PROVIDE (SPIEraseArea = 0x400001e4)
                [!provide]                        PROVIDE (SPILock = 0x400001e8)
                [!provide]                        PROVIDE (SPIMasterReadModeCnfig = 0x400001ec)
                [!provide]                        PROVIDE (SPI_Common_Command = 0x400001f0)
                [!provide]                        PROVIDE (SPI_WakeUp = 0x400001f4)
                [!provide]                        PROVIDE (SPI_block_erase = 0x400001f8)
                [!provide]                        PROVIDE (SPI_chip_erase = 0x400001fc)
                [!provide]                        PROVIDE (SPI_init = 0x40000200)
                [!provide]                        PROVIDE (SPI_page_program = 0x40000204)
                [!provide]                        PROVIDE (SPI_read_data = 0x40000208)
                [!provide]                        PROVIDE (SPI_sector_erase = 0x4000020c)
                [!provide]                        PROVIDE (SPI_write_enable = 0x40000210)
                [!provide]                        PROVIDE (SelectSpiFunction = 0x40000214)
                [!provide]                        PROVIDE (SetSpiDrvs = 0x40000218)
                [!provide]                        PROVIDE (Wait_SPI_Idle = 0x4000021c)
                [!provide]                        PROVIDE (spi_dummy_len_fix = 0x40000220)
                [!provide]                        PROVIDE (Disable_QMode = 0x40000224)
                [!provide]                        PROVIDE (Enable_QMode = 0x40000228)
                [!provide]                        PROVIDE (rom_spiflash_legacy_funcs = 0x3fcdfff4)
                0x3fcdfff0                        PROVIDE (rom_spiflash_legacy_data = 0x3fcdfff0)
                [!provide]                        PROVIDE (g_flash_guard_ops = 0x3fcdfff8)
                [!provide]                        PROVIDE (spi_flash_hal_poll_cmd_done = 0x4000022c)
                [!provide]                        PROVIDE (spi_flash_hal_device_config = 0x40000230)
                [!provide]                        PROVIDE (spi_flash_hal_configure_host_io_mode = 0x40000234)
                [!provide]                        PROVIDE (spi_flash_hal_common_command = 0x40000238)
                [!provide]                        PROVIDE (spi_flash_hal_read = 0x4000023c)
                [!provide]                        PROVIDE (spi_flash_hal_erase_chip = 0x40000240)
                [!provide]                        PROVIDE (spi_flash_hal_erase_sector = 0x40000244)
                [!provide]                        PROVIDE (spi_flash_hal_erase_block = 0x40000248)
                [!provide]                        PROVIDE (spi_flash_hal_program_page = 0x4000024c)
                [!provide]                        PROVIDE (spi_flash_hal_set_write_protect = 0x40000250)
                [!provide]                        PROVIDE (spi_flash_hal_host_idle = 0x40000254)
                [!provide]                        PROVIDE (spi_flash_chip_generic_probe = 0x40000258)
                [!provide]                        PROVIDE (spi_flash_chip_generic_detect_size = 0x4000025c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_write = 0x40000260)
                [!provide]                        PROVIDE (spi_flash_chip_generic_write_encrypted = 0x40000264)
                [!provide]                        PROVIDE (spi_flash_chip_generic_set_write_protect = 0x40000268)
                [!provide]                        PROVIDE (spi_flash_common_write_status_16b_wrsr = 0x4000026c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_reset = 0x40000270)
                [!provide]                        PROVIDE (spi_flash_chip_generic_erase_chip = 0x40000274)
                [!provide]                        PROVIDE (spi_flash_chip_generic_erase_sector = 0x40000278)
                [!provide]                        PROVIDE (spi_flash_chip_generic_erase_block = 0x4000027c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_page_program = 0x40000280)
                [!provide]                        PROVIDE (spi_flash_chip_generic_get_write_protect = 0x40000284)
                [!provide]                        PROVIDE (spi_flash_common_read_status_16b_rdsr_rdsr2 = 0x40000288)
                [!provide]                        PROVIDE (spi_flash_chip_generic_read_reg = 0x4000028c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_yield = 0x40000290)
                [!provide]                        PROVIDE (spi_flash_generic_wait_host_idle = 0x40000294)
                [!provide]                        PROVIDE (spi_flash_chip_generic_wait_idle = 0x40000298)
                [!provide]                        PROVIDE (spi_flash_chip_generic_config_host_io_mode = 0x4000029c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_read = 0x400002a0)
                [!provide]                        PROVIDE (spi_flash_common_read_status_8b_rdsr2 = 0x400002a4)
                [!provide]                        PROVIDE (spi_flash_chip_generic_get_io_mode = 0x400002a8)
                [!provide]                        PROVIDE (spi_flash_common_read_status_8b_rdsr = 0x400002ac)
                [!provide]                        PROVIDE (spi_flash_common_write_status_8b_wrsr = 0x400002b0)
                [!provide]                        PROVIDE (spi_flash_common_write_status_8b_wrsr2 = 0x400002b4)
                [!provide]                        PROVIDE (spi_flash_common_set_io_mode = 0x400002b8)
                [!provide]                        PROVIDE (spi_flash_chip_generic_set_io_mode = 0x400002bc)
                [!provide]                        PROVIDE (spi_flash_chip_gd_get_io_mode = 0x400002c0)
                [!provide]                        PROVIDE (spi_flash_chip_gd_probe = 0x400002c4)
                [!provide]                        PROVIDE (spi_flash_chip_gd_set_io_mode = 0x400002c8)
                [!provide]                        PROVIDE (spi_flash_chip_generic_config_data = 0x3fcdffec)
                [!provide]                        PROVIDE (memspi_host_read_id_hs = 0x400002cc)
                [!provide]                        PROVIDE (memspi_host_read_status_hs = 0x400002d0)
                [!provide]                        PROVIDE (memspi_host_flush_cache = 0x400002d4)
                [!provide]                        PROVIDE (memspi_host_erase_chip = 0x400002d8)
                [!provide]                        PROVIDE (memspi_host_erase_sector = 0x400002dc)
                [!provide]                        PROVIDE (memspi_host_erase_block = 0x400002e0)
                [!provide]                        PROVIDE (memspi_host_program_page = 0x400002e4)
                [!provide]                        PROVIDE (memspi_host_read = 0x400002e8)
                [!provide]                        PROVIDE (memspi_host_set_write_protect = 0x400002ec)
                [!provide]                        PROVIDE (memspi_host_set_max_read_len = 0x400002f0)
                [!provide]                        PROVIDE (memspi_host_read_data_slicer = 0x400002f4)
                [!provide]                        PROVIDE (memspi_host_write_data_slicer = 0x400002f8)
                [!provide]                        PROVIDE (esp_flash_chip_driver_initialized = 0x400002fc)
                [!provide]                        PROVIDE (esp_flash_read_id = 0x40000300)
                [!provide]                        PROVIDE (esp_flash_get_size = 0x40000304)
                [!provide]                        PROVIDE (esp_flash_erase_chip = 0x40000308)
                [!provide]                        PROVIDE (rom_esp_flash_erase_region = 0x4000030c)
                [!provide]                        PROVIDE (esp_flash_get_chip_write_protect = 0x40000310)
                [!provide]                        PROVIDE (esp_flash_set_chip_write_protect = 0x40000314)
                [!provide]                        PROVIDE (esp_flash_get_protectable_regions = 0x40000318)
                [!provide]                        PROVIDE (esp_flash_get_protected_region = 0x4000031c)
                [!provide]                        PROVIDE (esp_flash_set_protected_region = 0x40000320)
                [!provide]                        PROVIDE (esp_flash_read = 0x40000324)
                [!provide]                        PROVIDE (esp_flash_write = 0x40000328)
                [!provide]                        PROVIDE (esp_flash_write_encrypted = 0x4000032c)
                [!provide]                        PROVIDE (esp_flash_read_encrypted = 0x40000330)
                [!provide]                        PROVIDE (esp_flash_get_io_mode = 0x40000334)
                [!provide]                        PROVIDE (esp_flash_set_io_mode = 0x40000338)
                [!provide]                        PROVIDE (spi_flash_boot_attach = 0x4000033c)
                [!provide]                        PROVIDE (spi_flash_dump_counters = 0x40000340)
                [!provide]                        PROVIDE (spi_flash_get_counters = 0x40000344)
                [!provide]                        PROVIDE (spi_flash_op_counters_config = 0x40000348)
                [!provide]                        PROVIDE (spi_flash_reset_counters = 0x4000034c)
                [!provide]                        PROVIDE (esp_flash_default_chip = 0x3fcdffe8)
                [!provide]                        PROVIDE (esp_flash_api_funcs = 0x3fcdffe4)
                0x400004b0                        PROVIDE (Cache_Get_ICache_Line_Size = 0x400004b0)
                [!provide]                        PROVIDE (Cache_Get_Mode = 0x400004b4)
                [!provide]                        PROVIDE (Cache_Address_Through_IBus = 0x400004b8)
                [!provide]                        PROVIDE (Cache_Address_Through_DBus = 0x400004bc)
                [!provide]                        PROVIDE (Cache_Set_Default_Mode = 0x400004c0)
                [!provide]                        PROVIDE (Cache_Enable_Defalut_ICache_Mode = 0x400004c4)
                0x400004c8                        PROVIDE (ROM_Boot_Cache_Init = 0x400004c8)
                [!provide]                        PROVIDE (Cache_Invalidate_ICache_Items = 0x400004cc)
                [!provide]                        PROVIDE (Cache_Op_Addr = 0x400004d0)
                0x400004d4                        PROVIDE (Cache_Invalidate_Addr = 0x400004d4)
                [!provide]                        PROVIDE (Cache_Invalidate_ICache_All = 0x400004d8)
                [!provide]                        PROVIDE (Cache_Mask_All = 0x400004dc)
                [!provide]                        PROVIDE (Cache_UnMask_Dram0 = 0x400004e0)
                [!provide]                        PROVIDE (Cache_Suspend_ICache_Autoload = 0x400004e4)
                [!provide]                        PROVIDE (Cache_Resume_ICache_Autoload = 0x400004e8)
                [!provide]                        PROVIDE (Cache_Start_ICache_Preload = 0x400004ec)
                [!provide]                        PROVIDE (Cache_ICache_Preload_Done = 0x400004f0)
                [!provide]                        PROVIDE (Cache_End_ICache_Preload = 0x400004f4)
                [!provide]                        PROVIDE (Cache_Config_ICache_Autoload = 0x400004f8)
                [!provide]                        PROVIDE (Cache_Enable_ICache_Autoload = 0x400004fc)
                [!provide]                        PROVIDE (Cache_Disable_ICache_Autoload = 0x40000500)
                [!provide]                        PROVIDE (Cache_Enable_ICache_PreLock = 0x40000504)
                [!provide]                        PROVIDE (Cache_Disable_ICache_PreLock = 0x40000508)
                [!provide]                        PROVIDE (Cache_Lock_ICache_Items = 0x4000050c)
                [!provide]                        PROVIDE (Cache_Unlock_ICache_Items = 0x40000510)
                [!provide]                        PROVIDE (Cache_Lock_Addr = 0x40000514)
                [!provide]                        PROVIDE (Cache_Unlock_Addr = 0x40000518)
                0x4000051c                        PROVIDE (Cache_Disable_ICache = 0x4000051c)
                0x40000520                        PROVIDE (Cache_Enable_ICache = 0x40000520)
                0x40000524                        PROVIDE (Cache_Suspend_ICache = 0x40000524)
                0x40000528                        PROVIDE (Cache_Resume_ICache = 0x40000528)
                [!provide]                        PROVIDE (Cache_Freeze_ICache_Enable = 0x4000052c)
                [!provide]                        PROVIDE (Cache_Freeze_ICache_Disable = 0x40000530)
                [!provide]                        PROVIDE (Cache_Pms_Lock = 0x40000534)
                [!provide]                        PROVIDE (Cache_Ibus_Pms_Set_Addr = 0x40000538)
                [!provide]                        PROVIDE (Cache_Ibus_Pms_Set_Attr = 0x4000053c)
                [!provide]                        PROVIDE (Cache_Dbus_Pms_Set_Addr = 0x40000540)
                [!provide]                        PROVIDE (Cache_Dbus_Pms_Set_Attr = 0x40000544)
                [!provide]                        PROVIDE (Cache_Set_IDROM_MMU_Size = 0x40000548)
                [!provide]                        PROVIDE (Cache_Get_IROM_MMU_End = 0x4000054c)
                [!provide]                        PROVIDE (Cache_Get_DROM_MMU_End = 0x40000550)
                [!provide]                        PROVIDE (Cache_Owner_Init = 0x40000554)
                [!provide]                        PROVIDE (Cache_Occupy_ICache_MEMORY = 0x40000558)
                [!provide]                        PROVIDE (Cache_MMU_Init = 0x4000055c)
                [!provide]                        PROVIDE (Cache_Ibus_MMU_Set = 0x40000560)
                [!provide]                        PROVIDE (Cache_Dbus_MMU_Set = 0x40000564)
                [!provide]                        PROVIDE (Cache_Count_Flash_Pages = 0x40000568)
                [!provide]                        PROVIDE (Cache_Travel_Tag_Memory = 0x4000056c)
                [!provide]                        PROVIDE (Cache_Get_Virtual_Addr = 0x40000570)
                [!provide]                        PROVIDE (Cache_Get_Memory_BaseAddr = 0x40000574)
                [!provide]                        PROVIDE (Cache_Get_Memory_Addr = 0x40000578)
                [!provide]                        PROVIDE (Cache_Get_Memory_value = 0x4000057c)
                [!provide]                        PROVIDE (rom_cache_op_cb = 0x3fcdffd8)
                [!provide]                        PROVIDE (rom_cache_internal_table_ptr = 0x3fcdffd4)
                0x40000580                        ets_get_apb_freq = 0x40000580
                0x40000584                        ets_get_cpu_frequency = 0x40000584
                0x40000588                        ets_update_cpu_frequency = 0x40000588
                0x4000058c                        ets_get_printf_channel = 0x4000058c
                0x40000590                        ets_get_xtal_div = 0x40000590
                0x40000594                        ets_set_xtal_div = 0x40000594
                0x40000598                        ets_get_xtal_freq = 0x40000598
                0x4000059c                        gpio_input_get = 0x4000059c
                0x400005a0                        gpio_matrix_in = 0x400005a0
                0x400005a4                        gpio_matrix_out = 0x400005a4
                0x400005a8                        gpio_output_disable = 0x400005a8
                0x400005ac                        gpio_output_enable = 0x400005ac
                0x400005b0                        gpio_output_set = 0x400005b0
                0x400005b4                        gpio_pad_hold = 0x400005b4
                0x400005b8                        gpio_pad_input_disable = 0x400005b8
                0x400005bc                        gpio_pad_input_enable = 0x400005bc
                0x400005c0                        gpio_pad_pulldown = 0x400005c0
                0x400005c4                        gpio_pad_pullup = 0x400005c4
                0x400005c8                        gpio_pad_select_gpio = 0x400005c8
                0x400005cc                        gpio_pad_set_drv = 0x400005cc
                0x400005d0                        gpio_pad_unhold = 0x400005d0
                0x400005d4                        gpio_pin_wakeup_disable = 0x400005d4
                0x400005d8                        gpio_pin_wakeup_enable = 0x400005d8
                0x400005dc                        gpio_bypass_matrix_in = 0x400005dc
                0x400005e0                        esprv_intc_int_set_priority = 0x400005e0
                0x400005e4                        esprv_intc_int_set_threshold = 0x400005e4
                0x400005e8                        esprv_intc_int_enable = 0x400005e8
                0x400005ec                        esprv_intc_int_disable = 0x400005ec
                0x400005f0                        esprv_intc_int_set_type = 0x400005f0
                0x400005f4                        intr_matrix_set = 0x400005f4
                0x400005f8                        ets_intr_lock = 0x400005f8
                0x400005fc                        ets_intr_unlock = 0x400005fc
                [!provide]                        PROVIDE (intr_handler_set = 0x40000600)
                0x40000604                        ets_isr_attach = 0x40000604
                0x40000608                        ets_isr_mask = 0x40000608
                0x4000060c                        ets_isr_unmask = 0x4000060c
                0x40000610                        md5_vector = 0x40000610
                0x40000614                        MD5Init = 0x40000614
                0x40000618                        MD5Update = 0x40000618
                0x4000061c                        MD5Final = 0x4000061c
                0x40000620                        hmac_md5_vector = 0x40000620
                0x40000624                        hmac_md5 = 0x40000624
                0x40000628                        crc32_le = 0x40000628
                0x4000062c                        crc32_be = 0x4000062c
                0x40000630                        crc16_le = 0x40000630
                0x40000634                        crc16_be = 0x40000634
                0x40000638                        crc8_le = 0x40000638
                0x4000063c                        crc8_be = 0x4000063c
                0x40000640                        esp_crc8 = 0x40000640
                0x40000644                        ets_sha_enable = 0x40000644
                0x40000648                        ets_sha_disable = 0x40000648
                0x4000064c                        ets_sha_get_state = 0x4000064c
                0x40000650                        ets_sha_init = 0x40000650
                0x40000654                        ets_sha_process = 0x40000654
                0x40000658                        ets_sha_starts = 0x40000658
                0x4000065c                        ets_sha_update = 0x4000065c
                0x40000660                        ets_sha_finish = 0x40000660
                0x40000664                        ets_sha_clone = 0x40000664
                0x40000668                        ets_hmac_enable = 0x40000668
                0x4000066c                        ets_hmac_disable = 0x4000066c
                0x40000670                        ets_hmac_calculate_message = 0x40000670
                0x40000674                        ets_hmac_calculate_downstream = 0x40000674
                0x40000678                        ets_hmac_invalidate_downstream = 0x40000678
                0x4000067c                        ets_jtag_enable_temporarily = 0x4000067c
                0x40000680                        ets_aes_enable = 0x40000680
                0x40000684                        ets_aes_disable = 0x40000684
                0x40000688                        ets_aes_setkey = 0x40000688
                0x4000068c                        ets_aes_block = 0x4000068c
                0x40000690                        ets_bigint_enable = 0x40000690
                0x40000694                        ets_bigint_disable = 0x40000694
                0x40000698                        ets_bigint_multiply = 0x40000698
                0x4000069c                        ets_bigint_modmult = 0x4000069c
                0x400006a0                        ets_bigint_modexp = 0x400006a0
                0x400006a4                        ets_bigint_wait_finish = 0x400006a4
                0x400006a8                        ets_bigint_getz = 0x400006a8
                0x400006ac                        ets_ds_enable = 0x400006ac
                0x400006b0                        ets_ds_disable = 0x400006b0
                0x400006b4                        ets_ds_start_sign = 0x400006b4
                0x400006b8                        ets_ds_is_busy = 0x400006b8
                0x400006bc                        ets_ds_finish_sign = 0x400006bc
                0x400006c0                        ets_ds_encrypt_params = 0x400006c0
                0x400006c4                        ets_aes_setkey_dec = 0x400006c4
                0x400006c8                        ets_aes_setkey_enc = 0x400006c8
                0x400006cc                        ets_mgf1_sha256 = 0x400006cc
                0x400006d0                        ets_efuse_read = 0x400006d0
                0x400006d4                        ets_efuse_program = 0x400006d4
                0x400006d8                        ets_efuse_clear_program_registers = 0x400006d8
                0x400006dc                        ets_efuse_write_key = 0x400006dc
                0x400006e0                        ets_efuse_get_read_register_address = 0x400006e0
                0x400006e4                        ets_efuse_get_key_purpose = 0x400006e4
                0x400006e8                        ets_efuse_key_block_unused = 0x400006e8
                0x400006ec                        ets_efuse_find_unused_key_block = 0x400006ec
                0x400006f0                        ets_efuse_rs_calculate = 0x400006f0
                0x400006f4                        ets_efuse_count_unused_key_blocks = 0x400006f4
                0x400006f8                        ets_efuse_secure_boot_enabled = 0x400006f8
                0x400006fc                        ets_efuse_secure_boot_aggressive_revoke_enabled = 0x400006fc
                0x40000700                        ets_efuse_cache_encryption_enabled = 0x40000700
                0x40000704                        ets_efuse_download_modes_disabled = 0x40000704
                0x40000708                        ets_efuse_find_purpose = 0x40000708
                0x4000070c                        ets_efuse_flash_opi_5pads_power_sel_vddspi = 0x4000070c
                0x40000710                        ets_efuse_force_send_resume = 0x40000710
                0x40000714                        ets_efuse_get_flash_delay_us = 0x40000714
                0x40000718                        ets_efuse_get_mac = 0x40000718
                0x4000071c                        ets_efuse_get_spiconfig = 0x4000071c
                0x40000720                        ets_efuse_usb_print_is_disabled = 0x40000720
                0x40000724                        ets_efuse_usb_serial_jtag_print_is_disabled = 0x40000724
                0x40000728                        ets_efuse_get_uart_print_control = 0x40000728
                0x4000072c                        ets_efuse_get_wp_pad = 0x4000072c
                0x40000730                        ets_efuse_legacy_spi_boot_mode_disabled = 0x40000730
                0x40000734                        ets_efuse_security_download_modes_enabled = 0x40000734
                0x40000738                        ets_efuse_set_timing = 0x40000738
                0x4000073c                        ets_efuse_jtag_disabled = 0x4000073c
                0x40000740                        ets_efuse_usb_download_mode_disabled = 0x40000740
                0x40000744                        ets_efuse_usb_module_disabled = 0x40000744
                0x40000748                        ets_efuse_usb_device_disabled = 0x40000748
                0x4000074c                        ets_emsa_pss_verify = 0x4000074c
                0x40000750                        ets_rsa_pss_verify = 0x40000750
                0x40000754                        ets_secure_boot_verify_bootloader_with_keys = 0x40000754
                0x40000758                        ets_secure_boot_verify_signature = 0x40000758
                0x4000075c                        ets_secure_boot_read_key_digests = 0x4000075c
                0x40000760                        ets_secure_boot_revoke_public_key_digest = 0x40000760
                [!provide]                        PROVIDE (usb_uart_rx_one_char = 0x400008cc)
                [!provide]                        PROVIDE (usb_uart_rx_one_char_block = 0x400008d0)
                [!provide]                        PROVIDE (usb_uart_tx_flush = 0x400008d4)
                [!provide]                        PROVIDE (usb_uart_tx_one_char = 0x400008d8)
                0x3fcdffd1                        PROVIDE (g_uart_print = 0x3fcdffd1)
                0x3fcdffd0                        PROVIDE (g_usb_print = 0x3fcdffd0)
                0x3fcdffcc                        bt_rf_coex_cfg_p = 0x3fcdffcc
                0x3fcdffc8                        bt_rf_coex_hooks_p = 0x3fcdffc8
                0x3fcdffc4                        btdm_env_p = 0x3fcdffc4
                0x3fcdffc0                        g_rw_controller_task_handle = 0x3fcdffc0
                0x3fcdffbc                        g_rw_init_sem = 0x3fcdffbc
                0x3fcdffb8                        g_rw_schd_queue = 0x3fcdffb8
                0x3fcdffb4                        lld_init_env = 0x3fcdffb4
                0x3fcdffb0                        lld_rpa_renew_env = 0x3fcdffb0
                0x3fcdffac                        lld_scan_env = 0x3fcdffac
                0x3fcdffa8                        lld_scan_sync_env = 0x3fcdffa8
                0x3fcdffa4                        lld_test_env = 0x3fcdffa4
                0x3fcdffa0                        p_ble_util_buf_env = 0x3fcdffa0
                0x3fcdff9c                        p_lld_env = 0x3fcdff9c
                0x3fcdff98                        p_llm_env = 0x3fcdff98
                0x3fcdff94                        r_h4tl_eif_p = 0x3fcdff94
                0x3fcdff90                        r_hli_funcs_p = 0x3fcdff90
                0x3fcdff8c                        r_ip_funcs_p = 0x3fcdff8c
                0x3fcdff88                        r_modules_funcs_p = 0x3fcdff88
                0x3fcdff84                        r_osi_funcs_p = 0x3fcdff84
                0x3fcdff80                        r_plf_funcs_p = 0x3fcdff80
                0x3fcdff7c                        vhci_env_p = 0x3fcdff7c
                0x3fcdff78                        aa_gen = 0x3fcdff78
                0x3fcdff6c                        aes_env = 0x3fcdff6c
                0x3fcdff1c                        bt_rf_coex_cfg_cb = 0x3fcdff1c
                0x3fcdff18                        btdm_pwr_state = 0x3fcdff18
                0x3fcdff14                        btdm_slp_err = 0x3fcdff14
                0x3fcdff0c                        ecc_env = 0x3fcdff0c
                0x3fcdff04                        esp_handler = 0x3fcdff04
                0x3fcdfefc                        esp_vendor_cmd = 0x3fcdfefc
                0x3fcdfef8                        g_adv_delay_dis = 0x3fcdfef8
                0x3fcdfef4                        g_conflict_elt = 0x3fcdfef4
                0x3fcdfee4                        g_eif_api = 0x3fcdfee4
                0x3fcdfed8                        g_event_empty = 0x3fcdfed8
                0x3fcdfecc                        g_llc_state = 0x3fcdfecc
                0x3fcdfec8                        g_llm_state = 0x3fcdfec8
                0x3fcdfec4                        g_max_evt_env = 0x3fcdfec4
                0x3fcdfec0                        g_misc_state = 0x3fcdfec0
                0x3fcdfea4                        g_rma_rule_db = 0x3fcdfea4
                0x3fcdfe88                        g_rtp_rule_db = 0x3fcdfe88
                0x3fcdfe85                        g_scan_forever = 0x3fcdfe85
                0x3fcdfe84                        g_time_msb = 0x3fcdfe84
                0x3fcdfe5c                        h4tl_env = 0x3fcdfe5c
                0x3fcdfe38                        hci_env = 0x3fcdfe38
                0x3fcdfe34                        hci_ext_host = 0x3fcdfe34
                0x3fcdfe2c                        hci_fc_env = 0x3fcdfe2c
                0x3fcdfe00                        hci_tl_env = 0x3fcdfe00
                0x3fcdfdd0                        ke_env = 0x3fcdfdd0
                0x3fcdfd90                        ke_event_env = 0x3fcdfd90
                0x3fcdfd14                        ke_task_env = 0x3fcdfd14
                0x3fcdfcec                        llc_env = 0x3fcdfcec
                0x3fcdfcc4                        lld_adv_env = 0x3fcdfcc4
                0x3fcdfc9c                        lld_con_env = 0x3fcdfc9c
                0x3fcdfc94                        lld_exp_sync_pos_tab = 0x3fcdfc94
                0x3fcdfc6c                        lld_per_adv_env = 0x3fcdfc6c
                0x3fcdfc44                        lld_sync_env = 0x3fcdfc44
                0x3fcdfc38                        llm_le_adv_flow_env = 0x3fcdfc38
                0x3fcdfc34                        rw_sleep_enable = 0x3fcdfc34
                0x3fcdfc2c                        rwble_env = 0x3fcdfc2c
                0x3fcdfc10                        rwip_env = 0x3fcdfc10
                0x3fcdfc04                        rwip_param = 0x3fcdfc04
                0x3fcdfc00                        rwip_prog_delay = 0x3fcdfc00
                0x3fcdfbc8                        rwip_rf = 0x3fcdfbc8
                0x3fcdfbc0                        sch_alarm_env = 0x3fcdfbc0
                0x3fcdfbac                        sch_arb_env = 0x3fcdfbac
                0x3fcdfba4                        sch_plan_env = 0x3fcdfba4
                0x3fcdfaa0                        sch_prog_env = 0x3fcdfaa0
                0x3fcdfa40                        sch_slice_env = 0x3fcdfa40
                0x3fcdfa38                        sch_slice_params = 0x3fcdfa38
                0x3fcdfa30                        timer_env = 0x3fcdfa30
                0x3fcdfa2c                        unloaded_area = 0x3fcdfa2c
                0x3fcdfa28                        vshci_state = 0x3fcdfa28
                0x3fcdfa1c                        TASK_DESC_LLC = 0x3fcdfa1c
                0x3fcdfa10                        TASK_DESC_LLM = 0x3fcdfa10
                0x3fcdfa04                        TASK_DESC_VSHCI = 0x3fcdfa04
                0x3fcdf9fc                        co_default_bdaddr = 0x3fcdf9fc
                0x3fcdf9f8                        dbg_assert_block = 0x3fcdf9f8
                0x3fcdf9f4                        g_bt_plf_log_level = 0x3fcdf9f4
                0x3fcdf9d0                        hci_cmd_desc_tab_vs_esp = 0x3fcdf9d0
                0x3fcdf9b8                        hci_command_handler_tab_esp = 0x3fcdf9b8
                0x3fcdf9b4                        privacy_en = 0x3fcdf9b4
                0x3fcdf96c                        sdk_cfg_priv_opts = 0x3fcdf96c
                0x3ff1ffdc                        BasePoint_x_256 = 0x3ff1ffdc
                0x3ff1ffbc                        BasePoint_y_256 = 0x3ff1ffbc
                0x3ff1ff9c                        DebugE256PublicKey_x = 0x3ff1ff9c
                0x3ff1ff7c                        DebugE256PublicKey_y = 0x3ff1ff7c
                0x3ff1ff5c                        DebugE256SecretKey = 0x3ff1ff5c
                0x3ff1f7a0                        ECC_4Win_Look_up_table = 0x3ff1f7a0
                0x3ff1f79c                        LLM_AA_CT1 = 0x3ff1f79c
                0x3ff1f798                        LLM_AA_CT2 = 0x3ff1f798
                0x3ff1f790                        RF_TX_PW_CONV_TBL = 0x3ff1f790
                0x3ff1f784                        TASK_DESC_MISC = 0x3ff1f784
                0x3ff1f768                        adv_evt_prop2type = 0x3ff1f768
                0x3ff1f760                        adv_evt_type2prop = 0x3ff1f760
                0x3ff1f750                        aes_cmac_zero = 0x3ff1f750
                0x3ff1f740                        aes_k2_salt = 0x3ff1f740
                0x3ff1f738                        aes_k3_id64 = 0x3ff1f738
                0x3ff1f728                        aes_k3_salt = 0x3ff1f728
                0x3ff1f724                        aes_k4_id6 = 0x3ff1f724
                0x3ff1f714                        aes_k4_salt = 0x3ff1f714
                0x3ff1f6e8                        bigHexP256 = 0x3ff1f6e8
                0x3ff1f6e0                        byte_tx_time = 0x3ff1f6e0
                0x3ff1f6d8                        co_null_bdaddr = 0x3ff1f6d8
                0x3ff1f6d0                        co_phy_mask_to_rate = 0x3ff1f6d0
                0x3ff1f6c8                        co_phy_mask_to_value = 0x3ff1f6c8
                0x3ff1f6c4                        co_phy_to_rate = 0x3ff1f6c4
                0x3ff1f6c0                        co_phy_value_to_mask = 0x3ff1f6c0
                0x3ff1f6b8                        co_rate_to_byte_dur_us = 0x3ff1f6b8
                0x3ff1f6b0                        co_rate_to_phy = 0x3ff1f6b0
                0x3ff1f6ac                        co_rate_to_phy_mask = 0x3ff1f6ac
                0x3ff1f69c                        co_sca2ppm = 0x3ff1f69c
                0x3ff1f670                        coef_B = 0x3ff1f670
                0x3ff1f668                        connect_req_dur_tab = 0x3ff1f668
                0x3ff1f5e4                        ecc_Jacobian_InfinityPoint256 = 0x3ff1f5e4
                0x3ff1f518                        em_base_reg_lut = 0x3ff1f518
                0x3ff1f510                        fixed_tx_time = 0x3ff1f510
                0x3ff1f508                        h4tl_msgtype2hdrlen = 0x3ff1f508
                0x3ff1f4d8                        hci_cmd_desc_root_tab = 0x3ff1f4d8
                0x3ff1f46c                        hci_cmd_desc_tab_ctrl_bb = 0x3ff1f46c
                0x3ff1f43c                        hci_cmd_desc_tab_info_par = 0x3ff1f43c
                0x3ff1f0a0                        hci_cmd_desc_tab_le = 0x3ff1f0a0
                0x3ff1f088                        hci_cmd_desc_tab_lk_ctrl = 0x3ff1f088
                0x3ff1f07c                        hci_cmd_desc_tab_stat_par = 0x3ff1f07c
                0x3ff1f040                        hci_cmd_desc_tab_vs = 0x3ff1f040
                0x3ff1eff8                        hci_evt_desc_tab = 0x3ff1eff8
                0x3ff1ef58                        hci_evt_le_desc_tab = 0x3ff1ef58
                0x3ff1ef50                        hci_evt_le_desc_tab_esp = 0x3ff1ef50
                0x3ff1ef48                        hci_rsvd_evt_msk = 0x3ff1ef48
                0x3ff1ef44                        lld_aux_phy_to_rate = 0x3ff1ef44
                0x3ff1ef3c                        lld_init_max_aux_dur_tab = 0x3ff1ef3c
                0x3ff1ef34                        lld_scan_map_legacy_pdu_to_evt_type = 0x3ff1ef34
                0x3ff1ef2c                        lld_scan_max_aux_dur_tab = 0x3ff1ef2c
                0x3ff1ef24                        lld_sync_max_aux_dur_tab = 0x3ff1ef24
                0x3ff1ef1c                        llm_local_le_feats = 0x3ff1ef1c
                0x3ff1ef14                        llm_local_le_states = 0x3ff1ef14
                0x3ff1eeec                        llm_local_supp_cmds = 0x3ff1eeec
                0x3ff1eecc                        maxSecretKey_256 = 0x3ff1eecc
                0x3ff1eec4                        max_data_tx_time = 0x3ff1eec4
                0x3ff1eeb4                        one_bits = 0x3ff1eeb4
                0x3ff1eeac                        rwip_coex_cfg = 0x3ff1eeac
                0x3ff1ee94                        rwip_priority = 0x3ff1ee94
                0x3ff1ee48                        veryBigHexP256 = 0x3ff1ee48
                0x400015b0                        esp_pp_rom_version_get = 0x400015b0
                0x400015b4                        RC_GetBlockAckTime = 0x400015b4
                0x400015b8                        ebuf_list_remove = 0x400015b8
                0x400015c8                        GetAccess = 0x400015c8
                0x400015cc                        hal_mac_is_low_rate_enabled = 0x400015cc
                0x400015d0                        hal_mac_tx_get_blockack = 0x400015d0
                0x400015d8                        ic_get_trc = 0x400015d8
                0x400015e0                        ic_mac_init = 0x400015e0
                0x400015e4                        ic_interface_enabled = 0x400015e4
                0x400015e8                        is_lmac_idle = 0x400015e8
                0x400015f0                        lmacDiscardAgedMSDU = 0x400015f0
                0x400015fc                        lmacIsIdle = 0x400015fc
                0x40001600                        lmacIsLongFrame = 0x40001600
                0x40001608                        lmacPostTxComplete = 0x40001608
                0x4000160c                        lmacProcessAllTxTimeout = 0x4000160c
                0x40001610                        lmacProcessCollisions = 0x40001610
                0x40001614                        lmacProcessRxSucData = 0x40001614
                0x40001618                        lmacReachLongLimit = 0x40001618
                0x4000161c                        lmacReachShortLimit = 0x4000161c
                0x40001620                        lmacRecycleMPDU = 0x40001620
                0x40001624                        lmacRxDone = 0x40001624
                0x40001634                        mac_tx_set_duration = 0x40001634
                0x4000163c                        mac_tx_set_plcp0 = 0x4000163c
                0x40001644                        mac_tx_set_plcp2 = 0x40001644
                0x4000164c                        pm_disable_dream_timer = 0x4000164c
                0x40001650                        pm_disable_sleep_delay_timer = 0x40001650
                0x40001658                        pm_mac_wakeup = 0x40001658
                0x4000165c                        pm_mac_sleep = 0x4000165c
                0x40001660                        pm_enable_active_timer = 0x40001660
                0x40001664                        pm_enable_sleep_delay_timer = 0x40001664
                0x40001668                        pm_local_tsf_process = 0x40001668
                0x4000166c                        pm_set_beacon_filter = 0x4000166c
                0x40001670                        pm_is_in_wifi_slice_threshold = 0x40001670
                0x40001674                        pm_is_waked = 0x40001674
                0x40001680                        pm_on_data_rx = 0x40001680
                0x40001684                        pm_on_tbtt = 0x40001684
                0x4000169c                        pm_sleep_for = 0x4000169c
                0x400016a4                        ppAMPDU2Normal = 0x400016a4
                0x400016ac                        ppCalFrameTimes = 0x400016ac
                0x400016b0                        ppCalSubFrameLength = 0x400016b0
                0x400016b8                        ppCheckTxAMPDUlength = 0x400016b8
                0x400016bc                        ppDequeueRxq_Locked = 0x400016bc
                0x400016c0                        ppDequeueTxQ = 0x400016c0
                0x400016c4                        ppEmptyDelimiterLength = 0x400016c4
                0x400016c8                        ppEnqueueRxq = 0x400016c8
                0x400016cc                        ppEnqueueTxDone = 0x400016cc
                0x400016d0                        ppGetTxQFirstAvail_Locked = 0x400016d0
                0x400016d4                        ppGetTxframe = 0x400016d4
                0x400016e0                        ppProcessRxPktHdr = 0x400016e0
                0x400016e8                        ppRecordBarRRC = 0x400016e8
                0x400016ec                        lmacRequestTxopQueue = 0x400016ec
                0x400016f0                        lmacReleaseTxopQueue = 0x400016f0
                0x400016f4                        ppRecycleAmpdu = 0x400016f4
                0x400016f8                        ppRecycleRxPkt = 0x400016f8
                0x400016fc                        ppResortTxAMPDU = 0x400016fc
                0x40001700                        ppResumeTxAMPDU = 0x40001700
                0x4000170c                        ppRxProtoProc = 0x4000170c
                0x40001710                        ppSearchTxQueue = 0x40001710
                0x40001714                        ppSearchTxframe = 0x40001714
                0x40001718                        ppSelectNextQueue = 0x40001718
                0x4000171c                        ppSubFromAMPDU = 0x4000171c
                0x40001720                        ppTask = 0x40001720
                0x40001724                        ppTxPkt = 0x40001724
                0x40001728                        ppTxProtoProc = 0x40001728
                0x4000172c                        ppTxqUpdateBitmap = 0x4000172c
                0x40001734                        pp_hdrsize = 0x40001734
                0x40001738                        pp_post = 0x40001738
                0x4000173c                        pp_process_hmac_waiting_txq = 0x4000173c
                0x40001740                        rcGetAmpduSched = 0x40001740
                0x40001744                        rcUpdateRxDone = 0x40001744
                0x40001748                        rc_get_trc = 0x40001748
                0x4000174c                        rc_get_trc_by_index = 0x4000174c
                0x40001750                        rcAmpduLowerRate = 0x40001750
                0x40001754                        rcampduuprate = 0x40001754
                0x40001758                        rcClearCurAMPDUSched = 0x40001758
                0x4000175c                        rcClearCurSched = 0x4000175c
                0x40001760                        rcClearCurStat = 0x40001760
                0x40001768                        rcLowerSched = 0x40001768
                0x4000176c                        rcSetTxAmpduLimit = 0x4000176c
                0x40001774                        rcUpdateAckSnr = 0x40001774
                0x40001784                        rcUpSched = 0x40001784
                0x40001788                        rssi_margin = 0x40001788
                0x4000178c                        rx11NRate2AMPDULimit = 0x4000178c
                0x40001790                        TRC_AMPDU_PER_DOWN_THRESHOLD = 0x40001790
                0x40001794                        TRC_AMPDU_PER_UP_THRESHOLD = 0x40001794
                0x40001798                        trc_calc_duration = 0x40001798
                0x4000179c                        trc_isTxAmpduOperational = 0x4000179c
                0x400017a0                        trc_onAmpduOp = 0x400017a0
                0x400017a4                        TRC_PER_IS_GOOD = 0x400017a4
                0x400017a8                        trc_SetTxAmpduState = 0x400017a8
                0x400017ac                        trc_tid_isTxAmpduOperational = 0x400017ac
                0x400017b0                        trcAmpduSetState = 0x400017b0
                0x400017bc                        wDev_DiscardFrame = 0x400017bc
                0x400017c0                        wDev_GetNoiseFloor = 0x400017c0
                0x400017c4                        wDev_IndicateAmpdu = 0x400017c4
                0x400017cc                        wdev_bank_store = 0x400017cc
                0x400017d0                        wdev_bank_load = 0x400017d0
                0x400017d4                        wdev_mac_reg_load = 0x400017d4
                0x400017d8                        wdev_mac_reg_store = 0x400017d8
                0x400017dc                        wdev_mac_special_reg_load = 0x400017dc
                0x400017e0                        wdev_mac_special_reg_store = 0x400017e0
                0x400017e4                        wdev_mac_wakeup = 0x400017e4
                0x400017e8                        wdev_mac_sleep = 0x400017e8
                0x400017ec                        hal_mac_is_dma_enable = 0x400017ec
                0x400017f8                        wdevProcessRxSucDataAll = 0x400017f8
                0x400017fc                        wdev_csi_len_align = 0x400017fc
                0x40001800                        ppDequeueTxDone_Locked = 0x40001800
                0x4000180c                        config_is_cache_tx_buf_enabled = 0x4000180c
                0x40001810                        //ppMapWaitTxq = 0x40001810
                0x40001814                        ppProcessWaitingQueue = 0x40001814
                0x40001818                        ppDisableQueue = 0x40001818
                0x4000181c                        pm_allow_tx = 0x4000181c
                0x3ff1ee44                        our_instances_ptr = 0x3ff1ee44
                0x3fcdf968                        pTxRx = 0x3fcdf968
                0x3fcdf964                        lmacConfMib_ptr = 0x3fcdf964
                0x3fcdf960                        our_wait_eb = 0x3fcdf960
                0x3fcdf95c                        our_tx_eb = 0x3fcdf95c
                0x3fcdf958                        pp_wdev_funcs = 0x3fcdf958
                0x3fcdf954                        g_osi_funcs_p = 0x3fcdf954
                0x3fcdf950                        wDevCtrl_ptr = 0x3fcdf950
                0x3ff1ee40                        g_wdev_last_desc_reset_ptr = 0x3ff1ee40
                0x3fcdf94c                        wDevMacSleep_ptr = 0x3fcdf94c
                0x3fcdf948                        g_lmac_cnt_ptr = 0x3fcdf948
                0x3ff1ee3c                        our_controls_ptr = 0x3ff1ee3c
                0x3fcdf944                        pp_sig_cnt_ptr = 0x3fcdf944
                0x3fcdf940                        g_eb_list_desc_ptr = 0x3fcdf940
                0x3fcdf93c                        s_fragment_ptr = 0x3fcdf93c
                0x3fcdf938                        if_ctrl_ptr = 0x3fcdf938
                0x3fcdf934                        g_intr_lock_mux = 0x3fcdf934
                0x3fcdf930                        g_wifi_global_lock = 0x3fcdf930
                0x3fcdf92c                        s_wifi_queue = 0x3fcdf92c
                0x3fcdf928                        pp_task_hdl = 0x3fcdf928
                0x3fcdf924                        s_pp_task_create_sem = 0x3fcdf924
                0x3fcdf920                        s_pp_task_del_sem = 0x3fcdf920
                0x3fcdf91c                        g_wifi_menuconfig_ptr = 0x3fcdf91c
                0x3fcdf918                        xphyQueue = 0x3fcdf918
                0x3fcdf914                        ap_no_lr_ptr = 0x3fcdf914
                0x3fcdf910                        rc11BSchedTbl_ptr = 0x3fcdf910
                0x3fcdf90c                        rc11NSchedTbl_ptr = 0x3fcdf90c
                0x3fcdf908                        rcLoRaSchedTbl_ptr = 0x3fcdf908
                0x3fcdf904                        BasicOFDMSched_ptr = 0x3fcdf904
                0x3fcdf900                        trc_ctl_ptr = 0x3fcdf900
                0x3fcdf8fc                        g_pm_cnt_ptr = 0x3fcdf8fc
                0x3fcdf8f8                        g_pm_ptr = 0x3fcdf8f8
                0x3fcdf8f4                        g_pm_cfg_ptr = 0x3fcdf8f4
                0x3fcdf8f0                        g_esp_mesh_quick_funcs_ptr = 0x3fcdf8f0
                0x3fcdf8ec                        g_txop_queue_status_ptr = 0x3fcdf8ec
                0x3fcdf8e8                        g_mac_sleep_en_ptr = 0x3fcdf8e8
                0x3fcdf8e4                        g_mesh_is_root_ptr = 0x3fcdf8e4
                0x3fcdf8e0                        g_mesh_topology_ptr = 0x3fcdf8e0
                0x3fcdf8dc                        g_mesh_init_ps_type_ptr = 0x3fcdf8dc
                0x3fcdf8d8                        g_mesh_is_started_ptr = 0x3fcdf8d8
                0x3fcdf8d4                        g_config_func = 0x3fcdf8d4
                0x3fcdf8d0                        g_net80211_tx_func = 0x3fcdf8d0
                0x3fcdf8cc                        g_timer_func = 0x3fcdf8cc
                0x3fcdf8c8                        s_michael_mic_failure_cb = 0x3fcdf8c8
                0x3fcdf8c4                        wifi_sta_rx_probe_req = 0x3fcdf8c4
                0x3fcdf8c0                        g_tx_done_cb_func = 0x3fcdf8c0
                0x3fcdf874                        g_per_conn_trc = 0x3fcdf874
                0x3fcdf870                        s_encap_amsdu_func = 0x3fcdf870
                0x40001820                        esp_net80211_rom_version_get = 0x40001820
                0x40001824                        ampdu_dispatch = 0x40001824
                0x40001828                        ampdu_dispatch_all = 0x40001828
                0x4000182c                        ampdu_dispatch_as_many_as_possible = 0x4000182c
                0x40001830                        ampdu_dispatch_movement = 0x40001830
                0x40001834                        ampdu_dispatch_upto = 0x40001834
                0x40001838                        chm_is_at_home_channel = 0x40001838
                0x4000183c                        cnx_node_is_existing = 0x4000183c
                0x40001840                        cnx_node_search = 0x40001840
                0x40001844                        ic_ebuf_recycle_rx = 0x40001844
                0x40001848                        ic_ebuf_recycle_tx = 0x40001848
                0x4000184c                        ic_reset_rx_ba = 0x4000184c
                0x40001850                        ieee80211_align_eb = 0x40001850
                0x40001858                        ieee80211_ampdu_start_age_timer = 0x40001858
                0x40001860                        ieee80211_is_tx_allowed = 0x40001860
                0x40001864                        ieee80211_output_pending_eb = 0x40001864
                0x4000186c                        ieee80211_set_tx_desc = 0x4000186c
                0x40001870                        rom_sta_input = 0x40001870
                0x40001874                        wifi_get_macaddr = 0x40001874
                0x40001878                        wifi_rf_phy_disable = 0x40001878
                0x4000187c                        wifi_rf_phy_enable = 0x4000187c
                0x40001880                        ic_ebuf_alloc = 0x40001880
                0x40001888                        ieee80211_copy_eb_header = 0x40001888
                0x4000188c                        ieee80211_recycle_cache_eb = 0x4000188c
                0x40001890                        ieee80211_search_node = 0x40001890
                0x40001894                        roundup2 = 0x40001894
                0x40001898                        ieee80211_crypto_encap = 0x40001898
                0x400018a4                        ieee80211_set_tx_pti = 0x400018a4
                0x400018a8                        wifi_is_started = 0x400018a8
                0x3fcdf86c                        net80211_funcs = 0x3fcdf86c
                0x3fcdf868                        g_scan = 0x3fcdf868
                0x3fcdf864                        g_chm = 0x3fcdf864
                0x3fcdf860                        g_ic_ptr = 0x3fcdf860
                0x3fcdf85c                        g_hmac_cnt_ptr = 0x3fcdf85c
                0x3fcdf858                        g_tx_cacheq_ptr = 0x3fcdf858
                0x3fcdf854                        s_netstack_free = 0x3fcdf854
                0x3fcdf850                        mesh_rxcb = 0x3fcdf850
                0x3fcdf84c                        sta_rxcb = 0x3fcdf84c
                0x400018ac                        esp_coex_rom_version_get = 0x400018ac
                0x400018b0                        coex_bt_release = 0x400018b0
                0x400018b4                        coex_bt_request = 0x400018b4
                0x400018b8                        coex_core_ble_conn_dyn_prio_get = 0x400018b8
                0x400018c0                        coex_core_pti_get = 0x400018c0
                0x400018c4                        coex_core_release = 0x400018c4
                0x400018c8                        coex_core_request = 0x400018c8
                0x400018cc                        coex_core_status_get = 0x400018cc
                0x400018d4                        coex_event_duration_get = 0x400018d4
                0x400018d8                        coex_hw_timer_disable = 0x400018d8
                0x400018dc                        coex_hw_timer_enable = 0x400018dc
                0x400018e0                        coex_hw_timer_set = 0x400018e0
                0x400018e4                        coex_schm_interval_set = 0x400018e4
                0x400018e8                        coex_schm_lock = 0x400018e8
                0x400018ec                        coex_schm_unlock = 0x400018ec
                0x400018f4                        coex_wifi_release = 0x400018f4
                0x400018f8                        esp_coex_ble_conn_dynamic_prio_get = 0x400018f8
                0x3fcdf848                        coex_env_ptr = 0x3fcdf848
                0x3fcdf844                        coex_pti_tab_ptr = 0x3fcdf844
                0x3fcdf840                        coex_schm_env_ptr = 0x3fcdf840
                0x3fcdf83c                        coexist_funcs = 0x3fcdf83c
                0x3fcdf838                        g_coa_funcs_p = 0x3fcdf838
                0x3fcdf834                        g_coex_param_ptr = 0x3fcdf834
                0x400018fc                        phy_get_romfuncs = 0x400018fc
                0x40001900                        rom_abs_temp = 0x40001900
                0x40001904                        rom_bb_bss_cbw40_dig = 0x40001904
                0x40001908                        rom_bb_wdg_test_en = 0x40001908
                0x4000190c                        rom_bb_wdt_get_status = 0x4000190c
                0x40001910                        rom_bb_wdt_int_enable = 0x40001910
                0x40001914                        rom_bb_wdt_rst_enable = 0x40001914
                0x40001918                        rom_bb_wdt_timeout_clear = 0x40001918
                0x4000191c                        rom_cbw2040_cfg = 0x4000191c
                0x40001920                        rom_check_noise_floor = 0x40001920
                0x40001924                        rom_chip_i2c_readReg = 0x40001924
                0x40001928                        rom_chip_i2c_writeReg = 0x40001928
                0x4000192c                        rom_correct_rf_ana_gain = 0x4000192c
                0x40001930                        rom_dc_iq_est = 0x40001930
                0x40001934                        rom_disable_agc = 0x40001934
                0x40001938                        rom_en_pwdet = 0x40001938
                0x4000193c                        rom_enable_agc = 0x4000193c
                0x40001940                        rom_get_bbgain_db = 0x40001940
                0x40001944                        rom_get_data_sat = 0x40001944
                0x40001948                        rom_get_i2c_read_mask = 0x40001948
                0x4000194c                        rom_get_pwctrl_correct = 0x4000194c
                0x40001950                        rom_get_rf_gain_qdb = 0x40001950
                0x40001954                        rom_i2c_readReg = 0x40001954
                0x40001958                        rom_i2c_readReg_Mask = 0x40001958
                0x4000195c                        rom_i2c_writeReg = 0x4000195c
                0x40001960                        rom_i2c_writeReg_Mask = 0x40001960
                0x40001968                        rom_iq_est_disable = 0x40001968
                0x4000196c                        rom_iq_est_enable = 0x4000196c
                0x40001970                        rom_linear_to_db = 0x40001970
                0x40001974                        rom_loopback_mode_en = 0x40001974
                0x40001978                        rom_mhz2ieee = 0x40001978
                0x4000197c                        rom_noise_floor_auto_set = 0x4000197c
                0x40001980                        rom_pbus_debugmode = 0x40001980
                0x40001984                        rom_pbus_force_mode = 0x40001984
                0x40001988                        rom_pbus_force_test = 0x40001988
                0x4000198c                        rom_pbus_rd = 0x4000198c
                0x40001990                        rom_pbus_rd_addr = 0x40001990
                0x40001994                        rom_pbus_rd_shift = 0x40001994
                0x40001998                        rom_pbus_set_dco = 0x40001998
                0x4000199c                        rom_pbus_set_rxgain = 0x4000199c
                0x400019a0                        rom_pbus_workmode = 0x400019a0
                0x400019a4                        rom_pbus_xpd_rx_off = 0x400019a4
                0x400019a8                        rom_pbus_xpd_rx_on = 0x400019a8
                0x400019ac                        rom_pbus_xpd_tx_off = 0x400019ac
                0x400019b4                        rom_phy_byte_to_word = 0x400019b4
                0x400019b8                        rom_phy_disable_cca = 0x400019b8
                0x400019bc                        rom_phy_enable_cca = 0x400019bc
                0x400019c0                        rom_phy_get_noisefloor = 0x400019c0
                0x400019c4                        rom_phy_get_rx_freq = 0x400019c4
                0x400019c8                        rom_phy_set_bbfreq_init = 0x400019c8
                0x400019cc                        rom_pow_usr = 0x400019cc
                0x400019d0                        rom_pwdet_sar2_init = 0x400019d0
                0x400019d4                        rom_read_hw_noisefloor = 0x400019d4
                0x400019d8                        rom_read_sar_dout = 0x400019d8
                0x400019dc                        rom_set_cal_rxdc = 0x400019dc
                0x400019e0                        rom_set_chan_cal_interp = 0x400019e0
                0x400019e4                        rom_set_loopback_gain = 0x400019e4
                0x400019e8                        rom_set_noise_floor = 0x400019e8
                0x400019ec                        rom_set_rxclk_en = 0x400019ec
                0x400019f8                        rom_set_txclk_en = 0x400019f8
                0x400019fc                        rom_spur_cal = 0x400019fc
                0x40001a00                        rom_spur_reg_write_one_tone = 0x40001a00
                0x40001a04                        rom_target_power_add_backoff = 0x40001a04
                0x40001a08                        rom_tx_pwctrl_bg_init = 0x40001a08
                0x40001a10                        rom_wifi_11g_rate_chg = 0x40001a10
                0x40001a14                        rom_write_gain_mem = 0x40001a14
                0x40001a18                        chip726_phyrom_version = 0x40001a18
                0x40001a1c                        rom_disable_wifi_agc = 0x40001a1c
                0x40001a20                        rom_enable_wifi_agc = 0x40001a20
                0x40001a24                        rom_set_tx_gain_table = 0x40001a24
                0x40001a28                        rom_bt_index_to_bb = 0x40001a28
                0x40001a2c                        rom_bt_bb_to_index = 0x40001a2c
                0x40001a30                        rom_wr_bt_tx_atten = 0x40001a30
                0x40001a34                        rom_wr_bt_tx_gain_mem = 0x40001a34
                0x40001a38                        rom_spur_coef_cfg = 0x40001a38
                0x40001a3c                        rom_bb_bss_cbw40 = 0x40001a3c
                0x40001a40                        rom_set_cca = 0x40001a40
                0x40001a44                        rom_tx_paon_set = 0x40001a44
                0x40001a48                        rom_i2cmst_reg_init = 0x40001a48
                0x40001a4c                        rom_iq_corr_enable = 0x40001a4c
                0x40001a50                        rom_fe_reg_init = 0x40001a50
                0x40001a5c                        rom_mac_enable_bb = 0x40001a5c
                0x40001a60                        rom_bb_wdg_cfg = 0x40001a60
                0x40001a64                        rom_force_txon = 0x40001a64
                0x40001a68                        rom_fe_txrx_reset = 0x40001a68
                0x40001a6c                        rom_set_rx_comp = 0x40001a6c
                0x40001a74                        rom_write_chan_freq = 0x40001a74
                0x40001a7c                        rom_set_xpd_sar = 0x40001a7c
                0x40001a80                        rom_write_dac_gain2 = 0x40001a80
                0x40001a84                        rom_rtc_sar2_init = 0x40001a84
                0x40001a88                        rom_get_target_power_offset = 0x40001a88
                0x40001a90                        rom_get_rate_fcc_index = 0x40001a90
                0x40001a94                        rom_get_rate_target_power = 0x40001a94
                0x40001a98                        rom_write_wifi_dig_gain = 0x40001a98
                0x40001a9c                        rom_bt_correct_rf_ana_gain = 0x40001a9c
                0x40001aa0                        rom_pkdet_vol_start = 0x40001aa0
                0x40001aa4                        rom_read_sar2_code = 0x40001aa4
                0x40001aa8                        rom_get_sar2_vol = 0x40001aa8
                0x40001aac                        rom_get_pll_vol = 0x40001aac
                0x40001ab0                        rom_get_phy_target_power = 0x40001ab0
                0x40001ab8                        rom_phy_track_pll_cap = 0x40001ab8
                0x40001abc                        rom_phy_pwdet_always_en = 0x40001abc
                0x40001ac0                        rom_phy_pwdet_onetime_en = 0x40001ac0
                0x40001ac4                        rom_get_i2c_mst0_mask = 0x40001ac4
                0x40001ac8                        rom_get_i2c_hostid = 0x40001ac8
                0x40001acc                        rom_enter_critical_phy = 0x40001acc
                0x40001ad0                        rom_exit_critical_phy = 0x40001ad0
                0x40001ad4                        rom_chip_i2c_readReg_org = 0x40001ad4
                0x40001ad8                        rom_i2c_paral_set_mst0 = 0x40001ad8
                0x40001adc                        rom_i2c_paral_set_read = 0x40001adc
                0x40001ae0                        rom_i2c_paral_read = 0x40001ae0
                0x40001ae4                        rom_i2c_paral_write = 0x40001ae4
                0x40001ae8                        rom_i2c_paral_write_num = 0x40001ae8
                0x40001aec                        rom_i2c_paral_write_mask = 0x40001aec
                0x40001af0                        rom_bb_bss_cbw40_ana = 0x40001af0
                0x40001af4                        rom_chan_to_freq = 0x40001af4
                0x40001afc                        rom_dac_rate_set = 0x40001afc
                0x40001b08                        rom_tsens_index_to_dac = 0x40001b08
                0x40001b0c                        rom_tsens_index_to_offset = 0x40001b0c
                0x40001b14                        rom_code_to_temp = 0x40001b14
                0x40001b18                        rom_write_pll_cap_mem = 0x40001b18
                0x40001b1c                        rom_pll_correct_dcap = 0x40001b1c
                0x40001b20                        rom_phy_en_hw_set_freq = 0x40001b20
                0x40001b24                        rom_phy_dis_hw_set_freq = 0x40001b24
                0x40000628                        PROVIDE (esp_rom_crc32_le = crc32_le)
                [!provide]                        PROVIDE (esp_rom_crc16_le = crc16_le)
                [!provide]                        PROVIDE (esp_rom_crc8_le = crc8_le)
                [!provide]                        PROVIDE (esp_rom_crc32_be = crc32_be)
                [!provide]                        PROVIDE (esp_rom_crc16_be = crc16_be)
                [!provide]                        PROVIDE (esp_rom_crc8_be = crc8_be)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_select_gpio = gpio_pad_select_gpio)
                0x400005c4                        PROVIDE (esp_rom_gpio_pad_pullup_only = gpio_pad_pullup)
                0x400005cc                        PROVIDE (esp_rom_gpio_pad_set_drv = gpio_pad_set_drv)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_unhold = gpio_pad_unhold)
                0x400005a0                        PROVIDE (esp_rom_gpio_connect_in_signal = gpio_matrix_in)
                0x400005a4                        PROVIDE (esp_rom_gpio_connect_out_signal = gpio_matrix_out)
                [!provide]                        PROVIDE (esp_rom_efuse_mac_address_crc8 = esp_crc8)
                0x4000071c                        PROVIDE (esp_rom_efuse_get_flash_gpio_info = ets_efuse_get_spiconfig)
                [!provide]                        PROVIDE (esp_rom_efuse_is_secure_boot_enabled = ets_efuse_secure_boot_enabled)
                0x4000072c                        PROVIDE (esp_rom_efuse_get_flash_wp_gpio = ets_efuse_get_wp_pad)
                [!provide]                        PROVIDE (esp_rom_uart_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_uart_tx_one_char = uart_tx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_uart_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_rx_string = UartRxString)
                0x40000080                        PROVIDE (esp_rom_output_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_output_tx_one_char = uart_tx_one_char)
                0x40000084                        PROVIDE (esp_rom_output_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_output_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_output_rx_string = UartRxString)
                0x40000614                        PROVIDE (esp_rom_md5_init = MD5Init)
                0x40000618                        PROVIDE (esp_rom_md5_update = MD5Update)
                0x4000061c                        PROVIDE (esp_rom_md5_final = MD5Final)
                0x40000090                        PROVIDE (esp_rom_software_reset_system = software_reset)
                [!provide]                        PROVIDE (esp_rom_software_reset_cpu = software_reset_cpu)
                0x40000040                        PROVIDE (esp_rom_printf = ets_printf)
                0x40000050                        PROVIDE (esp_rom_delay_us = ets_delay_us)
                0x40000018                        PROVIDE (esp_rom_get_reset_reason = rtc_get_reset_reason)
                [!provide]                        PROVIDE (esp_rom_route_intr_matrix = intr_matrix_set)
                0x40000584                        PROVIDE (esp_rom_get_cpu_ticks_per_us = ets_get_cpu_frequency)
                0x40000588                        PROVIDE (esp_rom_set_cpu_ticks_per_us = ets_update_cpu_frequency)
                [!provide]                        PROVIDE (esp_rom_spiflash_clear_bp = esp_rom_spiflash_unlock)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_enable = SPI_write_enable)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_area = SPIEraseArea)
                [!provide]                        PROVIDE (esp_rom_spiflash_fix_dummylen = spi_dummy_len_fix)
                [!provide]                        PROVIDE (esp_rom_spiflash_set_drvs = SetSpiDrvs)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_padsfunc = SelectSpiFunction)
                [!provide]                        PROVIDE (esp_rom_spiflash_common_cmd = SPI_Common_Command)
                [!provide]                        PROVIDE (esp_rom_regi2c_read = rom_i2c_readReg)
                [!provide]                        PROVIDE (esp_rom_regi2c_read_mask = rom_i2c_readReg_Mask)
                0x4000195c                        PROVIDE (esp_rom_regi2c_write = rom_i2c_writeReg)
                0x40001960                        PROVIDE (esp_rom_regi2c_write_mask = rom_i2c_writeReg_Mask)
                0x400008dc                        bt_rf_coex_get_dft_cfg = 0x400008dc
                0x400008e0                        bt_rf_coex_hooks_p_set = 0x400008e0
                0x400008e4                        btdm_con_maxevtime_cal_impl = 0x400008e4
                0x400008e8                        btdm_controller_get_compile_version_impl = 0x400008e8
                0x400008ec                        btdm_controller_rom_data_init = 0x400008ec
                0x400008f0                        btdm_dis_privacy_err_report_impl = 0x400008f0
                0x400008f4                        btdm_disable_adv_delay_impl = 0x400008f4
                0x400008f8                        btdm_enable_scan_continue_impl = 0x400008f8
                0x400008fc                        btdm_enable_scan_forever_impl = 0x400008fc
                0x40000900                        btdm_get_power_state_impl = 0x40000900
                0x40000904                        btdm_get_prevent_sleep_flag_impl = 0x40000904
                0x40000908                        btdm_power_state_active_impl = 0x40000908
                0x4000090c                        btdm_switch_phy_coded_impl = 0x4000090c
                0x40000910                        hci_acl_data_handler = 0x40000910
                0x40000914                        hci_disconnect_cmd_handler = 0x40000914
                0x40000918                        hci_le_con_upd_cmd_handler = 0x40000918
                0x4000091c                        hci_le_ltk_req_neg_reply_cmd_handler = 0x4000091c
                0x40000920                        hci_le_ltk_req_reply_cmd_handler = 0x40000920
                0x40000924                        hci_le_rd_chnl_map_cmd_handler = 0x40000924
                0x40000928                        hci_le_rd_phy_cmd_handler = 0x40000928
                0x4000092c                        hci_le_rd_rem_feats_cmd_handler = 0x4000092c
                0x40000930                        hci_le_rem_con_param_req_neg_reply_cmd_handler = 0x40000930
                0x40000934                        hci_le_rem_con_param_req_reply_cmd_handler = 0x40000934
                0x40000938                        hci_le_set_data_len_cmd_handler = 0x40000938
                0x4000093c                        hci_le_set_phy_cmd_handler = 0x4000093c
                0x40000940                        hci_le_start_enc_cmd_handler = 0x40000940
                0x40000944                        hci_rd_auth_payl_to_cmd_handler = 0x40000944
                0x40000948                        hci_rd_rem_ver_info_cmd_handler = 0x40000948
                0x4000094c                        hci_rd_rssi_cmd_handler = 0x4000094c
                0x40000950                        hci_rd_tx_pwr_lvl_cmd_handler = 0x40000950
                0x40000954                        hci_vs_set_pref_slave_evt_dur_cmd_handler = 0x40000954
                0x40000958                        hci_vs_set_pref_slave_latency_cmd_handler = 0x40000958
                0x4000095c                        hci_wr_auth_payl_to_cmd_handler = 0x4000095c
                0x40000960                        ll_channel_map_ind_handler = 0x40000960
                0x40000964                        ll_connection_param_req_handler = 0x40000964
                0x40000968                        ll_connection_param_rsp_handler = 0x40000968
                0x4000096c                        ll_connection_update_ind_handler = 0x4000096c
                0x40000970                        ll_enc_req_handler = 0x40000970
                0x40000974                        ll_enc_rsp_handler = 0x40000974
                0x40000978                        ll_feature_req_handler = 0x40000978
                0x4000097c                        ll_feature_rsp_handler = 0x4000097c
                0x40000980                        ll_length_req_handler = 0x40000980
                0x40000984                        ll_length_rsp_handler = 0x40000984
                0x40000988                        ll_min_used_channels_ind_handler = 0x40000988
                0x4000098c                        ll_pause_enc_req_handler = 0x4000098c
                0x40000990                        ll_pause_enc_rsp_handler = 0x40000990
                0x40000994                        ll_phy_req_handler = 0x40000994
                0x40000998                        ll_phy_rsp_handler = 0x40000998
                0x4000099c                        ll_phy_update_ind_handler = 0x4000099c
                0x400009a0                        ll_ping_req_handler = 0x400009a0
                0x400009a4                        ll_ping_rsp_handler = 0x400009a4
                0x400009a8                        ll_slave_feature_req_handler = 0x400009a8
                0x400009ac                        ll_start_enc_req_handler = 0x400009ac
                0x400009b0                        ll_start_enc_rsp_handler = 0x400009b0
                0x400009b4                        ll_terminate_ind_handler = 0x400009b4
                0x400009b8                        ll_version_ind_handler = 0x400009b8
                0x400009bc                        llc_auth_payl_nearly_to_handler = 0x400009bc
                0x400009c0                        llc_auth_payl_real_to_handler = 0x400009c0
                0x400009c4                        llc_encrypt_ind_handler = 0x400009c4
                0x400009c8                        llc_hci_command_handler_wrapper = 0x400009c8
                0x400009cc                        llc_ll_connection_param_req_pdu_send = 0x400009cc
                0x400009d0                        llc_ll_connection_param_rsp_pdu_send = 0x400009d0
                0x400009d4                        llc_ll_connection_update_ind_pdu_send = 0x400009d4
                0x400009d8                        llc_ll_enc_req_pdu_send = 0x400009d8
                0x400009dc                        llc_ll_enc_rsp_pdu_send = 0x400009dc
                0x400009e0                        llc_ll_feature_req_pdu_send = 0x400009e0
                0x400009e4                        llc_ll_feature_rsp_pdu_send = 0x400009e4
                0x400009e8                        llc_ll_length_req_pdu_send = 0x400009e8
                0x400009ec                        llc_ll_length_rsp_pdu_send = 0x400009ec
                0x400009f0                        llc_ll_pause_enc_req_pdu_send = 0x400009f0
                0x400009f4                        llc_ll_pause_enc_rsp_pdu_send = 0x400009f4
                0x400009f8                        llc_ll_phy_req_pdu_send = 0x400009f8
                0x400009fc                        llc_ll_phy_rsp_pdu_send = 0x400009fc
                0x40000a00                        llc_ll_ping_req_pdu_send = 0x40000a00
                0x40000a04                        llc_ll_ping_rsp_pdu_send = 0x40000a04
                0x40000a08                        llc_ll_start_enc_req_pdu_send = 0x40000a08
                0x40000a0c                        llc_ll_start_enc_rsp_pdu_send = 0x40000a0c
                0x40000a10                        llc_ll_terminate_ind_pdu_send = 0x40000a10
                0x40000a14                        llc_ll_unknown_rsp_pdu_send = 0x40000a14
                0x40000a18                        llc_llcp_ch_map_update_ind_pdu_send = 0x40000a18
                0x40000a1c                        llc_llcp_phy_upd_ind_pdu_send = 0x40000a1c
                0x40000a20                        llc_llcp_version_ind_pdu_send = 0x40000a20
                0x40000a24                        llc_op_ch_map_upd_ind_handler = 0x40000a24
                0x40000a28                        llc_op_con_upd_ind_handler = 0x40000a28
                0x40000a2c                        llc_op_disconnect_ind_handler = 0x40000a2c
                0x40000a30                        llc_op_dl_upd_ind_handler = 0x40000a30
                0x40000a34                        llc_op_encrypt_ind_handler = 0x40000a34
                0x40000a38                        llc_op_feats_exch_ind_handler = 0x40000a38
                0x40000a3c                        llc_op_le_ping_ind_handler = 0x40000a3c
                0x40000a40                        llc_op_phy_upd_ind_handler = 0x40000a40
                0x40000a44                        llc_op_ver_exch_ind_handler = 0x40000a44
                0x40000a48                        llc_stopped_ind_handler = 0x40000a48
                0x40000a4c                        lld_acl_rx_ind_handler = 0x40000a4c
                0x40000a50                        lld_acl_tx_cfm_handler = 0x40000a50
                0x40000a54                        lld_adv_end_ind_handler = 0x40000a54
                0x40000a58                        lld_adv_rep_ind_handler = 0x40000a58
                0x40000a5c                        lld_ch_map_upd_cfm_handler = 0x40000a5c
                0x40000a60                        lld_con_estab_ind_handler = 0x40000a60
                0x40000a64                        lld_con_evt_sd_evt_time_set = 0x40000a64
                0x40000a68                        lld_con_offset_upd_ind_handler = 0x40000a68
                0x40000a6c                        lld_con_param_upd_cfm_handler = 0x40000a6c
                0x40000a70                        lld_disc_ind_handler = 0x40000a70
                0x40000a74                        lld_init_end_ind_handler = 0x40000a74
                0x40000a78                        lld_llcp_rx_ind_handler_wrapper = 0x40000a78
                0x40000a7c                        lld_llcp_tx_cfm_handler = 0x40000a7c
                0x40000a80                        lld_per_adv_end_ind_handler = 0x40000a80
                0x40000a84                        lld_per_adv_rep_ind_handler = 0x40000a84
                0x40000a88                        lld_per_adv_rx_end_ind_handler = 0x40000a88
                0x40000a8c                        lld_phy_coded_500k_get = 0x40000a8c
                0x40000a90                        lld_phy_upd_cfm_handler = 0x40000a90
                0x40000a94                        lld_scan_end_ind_handler = 0x40000a94
                0x40000a98                        lld_scan_req_ind_handler = 0x40000a98
                0x40000a9c                        lld_sync_start_req_handler = 0x40000a9c
                0x40000aa0                        lld_test_end_ind_handler = 0x40000aa0
                0x40000aa4                        lld_update_rxbuf_handler = 0x40000aa4
                0x40000aa8                        llm_ch_map_update_ind_handler = 0x40000aa8
                0x40000aac                        llm_hci_command_handler_wrapper = 0x40000aac
                0x40000ab0                        llm_scan_period_to_handler = 0x40000ab0
                0x40000ab4                        r_Add2SelfBigHex256 = 0x40000ab4
                0x40000ab8                        r_AddBigHex256 = 0x40000ab8
                0x40000abc                        r_AddBigHexModP256 = 0x40000abc
                0x40000ac0                        r_AddP256 = 0x40000ac0
                0x40000ac4                        r_AddPdiv2_256 = 0x40000ac4
                0x40000ac8                        r_GF_Jacobian_Point_Addition256 = 0x40000ac8
                0x40000acc                        r_GF_Jacobian_Point_Double256 = 0x40000acc
                0x40000ad0                        r_GF_Point_Jacobian_To_Affine256 = 0x40000ad0
                0x40000ad4                        r_MultiplyBigHexByUint32_256 = 0x40000ad4
                0x40000ad8                        r_MultiplyBigHexModP256 = 0x40000ad8
                0x40000adc                        r_MultiplyByU16ModP256 = 0x40000adc
                0x40000ae0                        r_SubtractBigHex256 = 0x40000ae0
                0x40000ae4                        r_SubtractBigHexMod256 = 0x40000ae4
                0x40000ae8                        r_SubtractBigHexUint32_256 = 0x40000ae8
                0x40000aec                        r_SubtractFromSelfBigHex256 = 0x40000aec
                0x40000af0                        r_SubtractFromSelfBigHexSign256 = 0x40000af0
                0x40000af4                        r_aes_alloc = 0x40000af4
                0x40000af8                        r_aes_ccm_continue = 0x40000af8
                0x40000afc                        r_aes_ccm_process_e = 0x40000afc
                0x40000b00                        r_aes_ccm_xor_128_lsb = 0x40000b00
                0x40000b04                        r_aes_ccm_xor_128_msb = 0x40000b04
                0x40000b08                        r_aes_cmac_continue = 0x40000b08
                0x40000b0c                        r_aes_cmac_start = 0x40000b0c
                0x40000b10                        r_aes_k1_continue = 0x40000b10
                0x40000b14                        r_aes_k2_continue = 0x40000b14
                0x40000b18                        r_aes_k3_continue = 0x40000b18
                0x40000b1c                        r_aes_k4_continue = 0x40000b1c
                0x40000b20                        r_aes_shift_left_128 = 0x40000b20
                0x40000b24                        r_aes_start = 0x40000b24
                0x40000b28                        r_aes_xor_128 = 0x40000b28
                0x40000b2c                        r_assert_err = 0x40000b2c
                0x40000b30                        r_assert_param = 0x40000b30
                0x40000b34                        r_assert_warn = 0x40000b34
                0x40000b38                        r_bigHexInversion256 = 0x40000b38
                0x40000b3c                        r_ble_sw_cca_check_isr = 0x40000b3c
                0x40000b40                        r_ble_util_buf_acl_tx_alloc = 0x40000b40
                0x40000b44                        r_ble_util_buf_acl_tx_elt_get = 0x40000b44
                0x40000b48                        r_ble_util_buf_acl_tx_free = 0x40000b48
                0x40000b4c                        r_ble_util_buf_acl_tx_free_in_isr = 0x40000b4c
                0x40000b50                        r_ble_util_buf_adv_tx_alloc = 0x40000b50
                0x40000b54                        r_ble_util_buf_adv_tx_free = 0x40000b54
                0x40000b58                        r_ble_util_buf_adv_tx_free_in_isr = 0x40000b58
                0x40000b5c                        r_ble_util_buf_env_deinit = 0x40000b5c
                0x40000b60                        r_ble_util_buf_env_init = 0x40000b60
                0x40000b64                        r_ble_util_buf_get_rx_buf_nb = 0x40000b64
                0x40000b68                        r_ble_util_buf_get_rx_buf_size = 0x40000b68
                0x40000b6c                        r_ble_util_buf_llcp_tx_alloc = 0x40000b6c
                0x40000b70                        r_ble_util_buf_llcp_tx_free = 0x40000b70
                0x40000b74                        r_ble_util_buf_rx_alloc = 0x40000b74
                0x40000b78                        r_ble_util_buf_rx_alloc_in_isr = 0x40000b78
                0x40000b7c                        r_ble_util_buf_rx_free = 0x40000b7c
                0x40000b80                        r_ble_util_buf_rx_free_in_isr = 0x40000b80
                0x40000b84                        r_ble_util_buf_set_rx_buf_nb = 0x40000b84
                0x40000b88                        r_ble_util_buf_set_rx_buf_size = 0x40000b88
                0x40000b8c                        r_ble_util_data_rx_buf_reset = 0x40000b8c
                0x40000b90                        r_bt_bb_get_intr_mask = 0x40000b90
                0x40000b94                        r_bt_bb_intr_clear = 0x40000b94
                0x40000b98                        r_bt_bb_intr_mask_set = 0x40000b98
                0x40000ba0                        r_bt_rf_coex_cfg_set = 0x40000ba0
                0x40000ba4                        r_bt_rf_coex_conn_dynamic_pti_en_get = 0x40000ba4
                0x40000bac                        r_bt_rf_coex_ext_adv_dynamic_pti_en_get = 0x40000bac
                0x40000bb0                        r_bt_rf_coex_ext_scan_dynamic_pti_en_get = 0x40000bb0
                0x40000bb4                        r_bt_rf_coex_legacy_adv_dynamic_pti_en_get = 0x40000bb4
                0x40000bb8                        r_bt_rf_coex_per_adv_dynamic_pti_en_get = 0x40000bb8
                0x40000bbc                        r_bt_rf_coex_pti_table_get = 0x40000bbc
                0x40000bc0                        r_bt_rf_coex_st_param_get = 0x40000bc0
                0x40000bc4                        r_bt_rf_coex_st_param_set = 0x40000bc4
                0x40000bc8                        r_bt_rf_coex_sync_scan_dynamic_pti_en_get = 0x40000bc8
                0x40000bcc                        r_bt_rma_apply_rule_cs_fmt = 0x40000bcc
                0x40000bd0                        r_bt_rma_apply_rule_cs_idx = 0x40000bd0
                0x40000bd4                        r_bt_rma_configure = 0x40000bd4
                0x40000bd8                        r_bt_rma_deregister_rule_cs_fmt = 0x40000bd8
                0x40000bdc                        r_bt_rma_deregister_rule_cs_idx = 0x40000bdc
                0x40000be0                        r_bt_rma_get_ant_by_act = 0x40000be0
                0x40000be4                        r_bt_rma_init = 0x40000be4
                0x40000be8                        r_bt_rma_register_rule_cs_fmt = 0x40000be8
                0x40000bec                        r_bt_rma_register_rule_cs_idx = 0x40000bec
                0x40000bf0                        r_bt_rtp_apply_rule_cs_fmt = 0x40000bf0
                0x40000bf4                        r_bt_rtp_apply_rule_cs_idx = 0x40000bf4
                0x40000bf8                        r_bt_rtp_deregister_rule_cs_fmt = 0x40000bf8
                0x40000bfc                        r_bt_rtp_deregister_rule_cs_idx = 0x40000bfc
                0x40000c04                        r_bt_rtp_init = 0x40000c04
                0x40000c08                        r_bt_rtp_register_rule_cs_fmt = 0x40000c08
                0x40000c0c                        r_bt_rtp_register_rule_cs_idx = 0x40000c0c
                0x40000c10                        r_btdm_isr = 0x40000c10
                0x40000c20                        r_cali_phase_match_p = 0x40000c20
                0x40000c24                        r_cmp_abs_time = 0x40000c24
                0x40000c28                        r_cmp_dest_id = 0x40000c28
                0x40000c2c                        r_cmp_timer_id = 0x40000c2c
                0x40000c30                        r_co_bdaddr_compare = 0x40000c30
                0x40000c34                        r_co_ble_pkt_dur_in_us = 0x40000c34
                0x40000c38                        r_co_list_extract = 0x40000c38
                0x40000c3c                        r_co_list_extract_after = 0x40000c3c
                0x40000c40                        r_co_list_extract_sublist = 0x40000c40
                0x40000c44                        r_co_list_find = 0x40000c44
                0x40000c48                        r_co_list_init = 0x40000c48
                0x40000c4c                        r_co_list_insert_after = 0x40000c4c
                0x40000c50                        r_co_list_insert_before = 0x40000c50
                0x40000c54                        r_co_list_merge = 0x40000c54
                0x40000c58                        r_co_list_pool_init = 0x40000c58
                0x40000c5c                        r_co_list_pop_front = 0x40000c5c
                0x40000c60                        r_co_list_push_back = 0x40000c60
                0x40000c64                        r_co_list_push_back_sublist = 0x40000c64
                0x40000c68                        r_co_list_push_front = 0x40000c68
                0x40000c6c                        r_co_list_size = 0x40000c6c
                0x40000c70                        r_co_nb_good_le_channels = 0x40000c70
                0x40000c74                        r_co_util_pack = 0x40000c74
                0x40000c78                        r_co_util_read_array_size = 0x40000c78
                0x40000c7c                        r_co_util_unpack = 0x40000c7c
                0x40000c80                        r_dbg_env_deinit = 0x40000c80
                0x40000c84                        r_dbg_env_init = 0x40000c84
                0x40000c88                        r_dbg_platform_reset_complete = 0x40000c88
                0x40000c8c                        r_dl_upd_proc_start = 0x40000c8c
                0x40000c90                        r_dump_data = 0x40000c90
                0x40000c94                        r_ecc_abort_key256_generation = 0x40000c94
                0x40000c98                        r_ecc_gen_new_public_key = 0x40000c98
                0x40000c9c                        r_ecc_gen_new_secret_key = 0x40000c9c
                0x40000ca0                        r_ecc_generate_key256 = 0x40000ca0
                0x40000ca4                        r_ecc_get_debug_Keys = 0x40000ca4
                0x40000ca8                        r_ecc_init = 0x40000ca8
                0x40000cac                        r_ecc_is_valid_point = 0x40000cac
                0x40000cb0                        r_ecc_multiplication_event_handler = 0x40000cb0
                0x40000cb4                        r_ecc_point_multiplication_win_256 = 0x40000cb4
                0x40000cb8                        r_emi_alloc_em_mapping_by_offset = 0x40000cb8
                0x40000cbc                        r_emi_base_reg_lut_show = 0x40000cbc
                0x40000cc0                        r_emi_em_base_reg_show = 0x40000cc0
                0x40000cc4                        r_emi_free_em_mapping_by_offset = 0x40000cc4
                0x40000cc8                        r_emi_get_em_mapping_idx_by_offset = 0x40000cc8
                0x40000ccc                        r_emi_get_mem_addr_by_offset = 0x40000ccc
                0x40000cd0                        r_emi_overwrite_em_mapping_by_offset = 0x40000cd0
                0x40000cd4                        r_esp_vendor_hci_command_handler = 0x40000cd4
                0x40000cd8                        r_get_stack_usage = 0x40000cd8
                0x40000cdc                        r_h4tl_acl_hdr_rx_evt_handler = 0x40000cdc
                0x40000ce0                        r_h4tl_cmd_hdr_rx_evt_handler = 0x40000ce0
                0x40000ce4                        r_h4tl_cmd_pld_rx_evt_handler = 0x40000ce4
                0x40000ce8                        r_h4tl_eif_io_event_post = 0x40000ce8
                0x40000cec                        r_h4tl_eif_register = 0x40000cec
                0x40000cf0                        r_h4tl_init = 0x40000cf0
                0x40000cf4                        r_h4tl_out_of_sync = 0x40000cf4
                0x40000cf8                        r_h4tl_out_of_sync_check = 0x40000cf8
                0x40000cfc                        r_h4tl_read_hdr = 0x40000cfc
                0x40000d00                        r_h4tl_read_next_out_of_sync = 0x40000d00
                0x40000d04                        r_h4tl_read_payl = 0x40000d04
                0x40000d08                        r_h4tl_read_start = 0x40000d08
                0x40000d0c                        r_h4tl_rx_acl_hdr_extract = 0x40000d0c
                0x40000d10                        r_h4tl_rx_cmd_hdr_extract = 0x40000d10
                0x40000d14                        r_h4tl_rx_done = 0x40000d14
                0x40000d18                        r_h4tl_start = 0x40000d18
                0x40000d1c                        r_h4tl_stop = 0x40000d1c
                0x40000d20                        r_h4tl_tx_done = 0x40000d20
                0x40000d24                        r_h4tl_tx_evt_handler = 0x40000d24
                0x40000d28                        r_h4tl_write = 0x40000d28
                0x40000d2c                        r_hci_acl_tx_data_alloc = 0x40000d2c
                0x40000d30                        r_hci_acl_tx_data_received = 0x40000d30
                0x40000d34                        r_hci_basic_cmd_send_2_controller = 0x40000d34
                0x40000d38                        r_hci_ble_adv_report_filter_check = 0x40000d38
                0x40000d3c                        r_hci_ble_adv_report_tx_check = 0x40000d3c
                0x40000d40                        r_hci_ble_conhdl_register = 0x40000d40
                0x40000d44                        r_hci_ble_conhdl_unregister = 0x40000d44
                0x40000d48                        r_hci_build_acl_data = 0x40000d48
                0x40000d4c                        r_hci_build_cc_evt = 0x40000d4c
                0x40000d50                        r_hci_build_cs_evt = 0x40000d50
                0x40000d54                        r_hci_build_evt = 0x40000d54
                0x40000d58                        r_hci_build_le_evt = 0x40000d58
                0x40000d5c                        r_hci_cmd_get_max_param_size = 0x40000d5c
                0x40000d60                        r_hci_cmd_received = 0x40000d60
                0x40000d64                        r_hci_cmd_reject = 0x40000d64
                0x40000d68                        r_hci_evt_mask_check = 0x40000d68
                0x40000d6c                        r_hci_evt_mask_set = 0x40000d6c
                0x40000d70                        r_hci_fc_acl_buf_size_set = 0x40000d70
                0x40000d74                        r_hci_fc_acl_en = 0x40000d74
                0x40000d78                        r_hci_fc_acl_packet_sent = 0x40000d78
                0x40000d7c                        r_hci_fc_check_host_available_nb_acl_packets = 0x40000d7c
                0x40000d80                        r_hci_fc_host_nb_acl_pkts_complete = 0x40000d80
                0x40000d84                        r_hci_fc_init = 0x40000d84
                0x40000d88                        r_hci_look_for_cmd_desc = 0x40000d88
                0x40000d8c                        r_hci_look_for_evt_desc = 0x40000d8c
                0x40000d90                        r_hci_look_for_le_evt_desc = 0x40000d90
                0x40000d94                        r_hci_look_for_le_evt_desc_esp = 0x40000d94
                0x40000d98                        r_hci_pack_bytes = 0x40000d98
                0x40000da0                        r_hci_send_2_controller = 0x40000da0
                0x40000da4                        r_hci_send_2_host = 0x40000da4
                0x40000da8                        r_hci_tl_c2h_data_flow_on = 0x40000da8
                0x40000dac                        r_hci_tl_cmd_hdr_rx_evt_handler = 0x40000dac
                0x40000db0                        r_hci_tl_cmd_pld_rx_evt_handler = 0x40000db0
                0x40000db4                        r_hci_tl_get_pkt = 0x40000db4
                0x40000db8                        r_hci_tl_hci_pkt_handler = 0x40000db8
                0x40000dbc                        r_hci_tl_hci_tx_done_evt_handler = 0x40000dbc
                0x40000dc0                        r_hci_tl_inc_nb_h2c_cmd_pkts = 0x40000dc0
                0x40000dc4                        r_hci_tl_save_pkt = 0x40000dc4
                0x40000dc8                        r_hci_tl_send = 0x40000dc8
                0x40000dcc                        r_hci_tx_done = 0x40000dcc
                0x40000dd0                        r_hci_tx_start = 0x40000dd0
                0x40000dd4                        r_hci_tx_trigger = 0x40000dd4
                0x40000dd8                        r_isValidSecretKey_256 = 0x40000dd8
                0x40000ddc                        r_ke_check_malloc = 0x40000ddc
                0x40000de0                        r_ke_event_callback_set = 0x40000de0
                0x40000de4                        r_ke_event_clear = 0x40000de4
                0x40000de8                        r_ke_event_flush = 0x40000de8
                0x40000dec                        r_ke_event_get = 0x40000dec
                0x40000df0                        r_ke_event_get_all = 0x40000df0
                0x40000df4                        r_ke_event_init = 0x40000df4
                0x40000df8                        r_ke_event_schedule = 0x40000df8
                0x40000dfc                        r_ke_event_set = 0x40000dfc
                0x40000e00                        r_ke_flush = 0x40000e00
                0x40000e04                        r_ke_free = 0x40000e04
                0x40000e08                        r_ke_handler_search = 0x40000e08
                0x40000e0c                        r_ke_init = 0x40000e0c
                0x40000e10                        r_ke_is_free = 0x40000e10
                0x40000e14                        r_ke_malloc = 0x40000e14
                0x40000e18                        r_ke_mem_init = 0x40000e18
                0x40000e1c                        r_ke_mem_is_empty = 0x40000e1c
                0x40000e20                        r_ke_mem_is_in_heap = 0x40000e20
                0x40000e24                        r_ke_msg_alloc = 0x40000e24
                0x40000e28                        r_ke_msg_dest_id_get = 0x40000e28
                0x40000e2c                        r_ke_msg_discard = 0x40000e2c
                0x40000e30                        r_ke_msg_forward = 0x40000e30
                0x40000e34                        r_ke_msg_forward_new_id = 0x40000e34
                0x40000e38                        r_ke_msg_free = 0x40000e38
                0x40000e3c                        r_ke_msg_in_queue = 0x40000e3c
                0x40000e40                        r_ke_msg_save = 0x40000e40
                0x40000e44                        r_ke_msg_send = 0x40000e44
                0x40000e48                        r_ke_msg_send_basic = 0x40000e48
                0x40000e4c                        r_ke_msg_src_id_get = 0x40000e4c
                0x40000e50                        r_ke_queue_extract = 0x40000e50
                0x40000e54                        r_ke_queue_insert = 0x40000e54
                0x40000e58                        r_ke_sleep_check = 0x40000e58
                0x40000e5c                        r_ke_state_get = 0x40000e5c
                0x40000e60                        r_ke_state_set = 0x40000e60
                0x40000e64                        r_ke_task_check = 0x40000e64
                0x40000e68                        r_ke_task_create = 0x40000e68
                0x40000e6c                        r_ke_task_delete = 0x40000e6c
                0x40000e70                        r_ke_task_handler_get = 0x40000e70
                0x40000e74                        r_ke_task_init = 0x40000e74
                0x40000e78                        r_ke_task_msg_flush = 0x40000e78
                0x40000e7c                        r_ke_task_saved_update = 0x40000e7c
                0x40000e84                        r_ke_time = 0x40000e84
                0x40000e88                        r_ke_time_cmp = 0x40000e88
                0x40000e8c                        r_ke_time_past = 0x40000e8c
                0x40000e90                        r_ke_timer_active = 0x40000e90
                0x40000e94                        r_ke_timer_adjust_all = 0x40000e94
                0x40000e98                        r_ke_timer_clear = 0x40000e98
                0x40000e9c                        r_ke_timer_init = 0x40000e9c
                0x40000ea0                        r_ke_timer_schedule = 0x40000ea0
                0x40000ea4                        r_ke_timer_set = 0x40000ea4
                0x40000ea8                        r_led_init = 0x40000ea8
                0x40000eac                        r_led_set_all = 0x40000eac
                0x40000eb0                        r_llc_aes_res_cb = 0x40000eb0
                0x40000eb4                        r_llc_ch_map_up_proc_err_cb = 0x40000eb4
                0x40000eb8                        r_llc_cleanup = 0x40000eb8
                0x40000ebc                        r_llc_cmd_cmp_send = 0x40000ebc
                0x40000ec0                        r_llc_cmd_stat_send = 0x40000ec0
                0x40000ec4                        r_llc_con_move_cbk = 0x40000ec4
                0x40000ec8                        r_llc_con_plan_set_update = 0x40000ec8
                0x40000ecc                        r_llc_con_upd_param_in_range = 0x40000ecc
                0x40000ed0                        r_llc_disconnect = 0x40000ed0
                0x40000ed4                        r_llc_disconnect_end = 0x40000ed4
                0x40000ed8                        r_llc_disconnect_proc_continue = 0x40000ed8
                0x40000edc                        r_llc_disconnect_proc_err_cb = 0x40000edc
                0x40000ee0                        r_llc_dl_chg_check = 0x40000ee0
                0x40000ee4                        r_llc_dle_proc_err_cb = 0x40000ee4
                0x40000ee8                        r_llc_feats_exch_proc_err_cb = 0x40000ee8
                0x40000eec                        r_llc_hci_cmd_handler_tab_p_get = 0x40000eec
                0x40000ef4                        r_llc_hci_con_param_req_evt_send = 0x40000ef4
                0x40000ef8                        r_llc_hci_con_upd_info_send = 0x40000ef8
                0x40000efc                        r_llc_hci_disconnected_dis = 0x40000efc
                0x40000f00                        r_llc_hci_dl_upd_info_send = 0x40000f00
                0x40000f04                        r_llc_hci_enc_evt_send = 0x40000f04
                0x40000f08                        r_llc_hci_feats_info_send = 0x40000f08
                0x40000f0c                        r_llc_hci_le_phy_upd_cmp_evt_send = 0x40000f0c
                0x40000f10                        r_llc_hci_ltk_request_evt_send = 0x40000f10
                0x40000f14                        r_llc_hci_nb_cmp_pkts_evt_send = 0x40000f14
                0x40000f18                        r_llc_hci_version_info_send = 0x40000f18
                0x40000f1c                        r_llc_init_term_proc = 0x40000f1c
                0x40000f20                        r_llc_iv_skd_rand_gen = 0x40000f20
                0x40000f24                        r_llc_le_ping_proc_continue = 0x40000f24
                0x40000f28                        r_llc_le_ping_proc_err_cb = 0x40000f28
                0x40000f30                        r_llc_le_ping_set = 0x40000f30
                0x40000f34                        r_llc_ll_pause_enc_rsp_ack_handler = 0x40000f34
                0x40000f38                        r_llc_ll_reject_ind_ack_handler = 0x40000f38
                0x40000f3c                        r_llc_ll_reject_ind_pdu_send = 0x40000f3c
                0x40000f40                        r_llc_ll_start_enc_rsp_ack_handler = 0x40000f40
                0x40000f44                        r_llc_ll_terminate_ind_ack = 0x40000f44
                0x40000f48                        r_llc_ll_unknown_ind_handler = 0x40000f48
                0x40000f4c                        r_llc_llcp_send = 0x40000f4c
                0x40000f50                        r_llc_llcp_state_set = 0x40000f50
                0x40000f54                        r_llc_llcp_trans_timer_set = 0x40000f54
                0x40000f58                        r_llc_llcp_tx_check = 0x40000f58
                0x40000f64                        r_llc_loc_con_upd_proc_err_cb = 0x40000f64
                0x40000f68                        r_llc_loc_dl_upd_proc_continue = 0x40000f68
                0x40000f6c                        r_llc_loc_encrypt_proc_continue = 0x40000f6c
                0x40000f70                        r_llc_loc_encrypt_proc_err_cb = 0x40000f70
                0x40000f74                        r_llc_loc_feats_exch_proc_continue = 0x40000f74
                0x40000f7c                        r_llc_loc_phy_upd_proc_err_cb = 0x40000f7c
                0x40000f80                        r_llc_msg_handler_tab_p_get = 0x40000f80
                0x40000f84                        r_llc_pref_param_compute = 0x40000f84
                0x40000f88                        r_llc_proc_collision_check = 0x40000f88
                0x40000f8c                        r_llc_proc_err_ind = 0x40000f8c
                0x40000f90                        r_llc_proc_get = 0x40000f90
                0x40000f94                        r_llc_proc_id_get = 0x40000f94
                0x40000f98                        r_llc_proc_reg = 0x40000f98
                0x40000f9c                        r_llc_proc_state_get = 0x40000f9c
                0x40000fa0                        r_llc_proc_state_set = 0x40000fa0
                0x40000fa4                        r_llc_proc_timer_pause_set = 0x40000fa4
                0x40000fa8                        r_llc_proc_timer_set = 0x40000fa8
                0x40000fac                        r_llc_proc_unreg = 0x40000fac
                0x40000fb0                        r_llc_rem_ch_map_proc_continue = 0x40000fb0
                0x40000fb8                        r_llc_rem_con_upd_proc_err_cb = 0x40000fb8
                0x40000fbc                        r_llc_rem_dl_upd_proc = 0x40000fbc
                0x40000fc0                        r_llc_rem_encrypt_proc_continue = 0x40000fc0
                0x40000fc4                        r_llc_rem_encrypt_proc_err_cb = 0x40000fc4
                0x40000fc8                        r_llc_rem_phy_upd_proc_continue = 0x40000fc8
                0x40000fcc                        r_llc_rem_phy_upd_proc_err_cb = 0x40000fcc
                0x40000fd0                        r_llc_role_get = 0x40000fd0
                0x40000fd4                        r_llc_sk_gen = 0x40000fd4
                0x40000fd8                        r_llc_start = 0x40000fd8
                0x40000fdc                        r_llc_stop = 0x40000fdc
                0x40000fe0                        r_llc_ver_exch_loc_proc_continue = 0x40000fe0
                0x40000fe4                        r_llc_ver_proc_err_cb = 0x40000fe4
                0x40000fe8                        r_llcp_pdu_handler_tab_p_get = 0x40000fe8
                0x40000fec                        r_lld_aa_gen = 0x40000fec
                0x40000ff0                        r_lld_adv_adv_data_set = 0x40000ff0
                0x40000ff4                        r_lld_adv_adv_data_update = 0x40000ff4
                0x40000ff8                        r_lld_adv_aux_ch_idx_set = 0x40000ff8
                0x40000ffc                        r_lld_adv_aux_evt_canceled_cbk = 0x40000ffc
                0x40001000                        r_lld_adv_aux_evt_start_cbk = 0x40001000
                0x40001004                        r_lld_adv_coex_check_ext_adv_synced = 0x40001004
                0x40001008                        r_lld_adv_coex_env_reset = 0x40001008
                0x4000100c                        r_lld_adv_duration_update = 0x4000100c
                0x40001010                        r_lld_adv_dynamic_pti_process = 0x40001010
                0x40001014                        r_lld_adv_end = 0x40001014
                0x40001018                        r_lld_adv_evt_canceled_cbk = 0x40001018
                0x4000101c                        r_lld_adv_evt_start_cbk = 0x4000101c
                0x40001020                        r_lld_adv_ext_chain_construct = 0x40001020
                0x40001024                        r_lld_adv_ext_pkt_prepare = 0x40001024
                0x40001028                        r_lld_adv_frm_cbk = 0x40001028
                0x4000102c                        r_lld_adv_frm_isr = 0x4000102c
                0x40001030                        r_lld_adv_frm_skip_isr = 0x40001030
                0x40001034                        r_lld_adv_init = 0x40001034
                0x40001038                        r_lld_adv_pkt_rx = 0x40001038
                0x4000103c                        r_lld_adv_pkt_rx_connect_ind = 0x4000103c
                0x40001044                        r_lld_adv_rand_addr_update = 0x40001044
                0x40001048                        r_lld_adv_restart = 0x40001048
                0x4000104c                        r_lld_adv_scan_rsp_data_set = 0x4000104c
                0x40001050                        r_lld_adv_scan_rsp_data_update = 0x40001050
                0x40001054                        r_lld_adv_set_tx_power = 0x40001054
                0x40001058                        r_lld_adv_start = 0x40001058
                0x4000105c                        r_lld_adv_stop = 0x4000105c
                0x40001060                        r_lld_adv_sync_info_set = 0x40001060
                0x40001064                        r_lld_adv_sync_info_update = 0x40001064
                0x40001068                        r_lld_calc_aux_rx = 0x40001068
                0x4000106c                        r_lld_cca_alloc = 0x4000106c
                0x40001070                        r_lld_cca_data_reset = 0x40001070
                0x40001074                        r_lld_cca_free = 0x40001074
                0x40001078                        r_lld_ch_assess_data_get = 0x40001078
                0x4000107c                        r_lld_ch_idx_get = 0x4000107c
                0x40001080                        r_lld_ch_map_set = 0x40001080
                0x40001084                        r_lld_channel_assess = 0x40001084
                0x40001088                        r_lld_con_activity_act_offset_compute = 0x40001088
                0x4000108c                        r_lld_con_activity_offset_compute = 0x4000108c
                0x40001090                        r_lld_con_ch_map_update = 0x40001090
                0x40001094                        r_lld_con_cleanup = 0x40001094
                0x40001098                        r_lld_con_current_tx_power_get = 0x40001098
                0x4000109c                        r_lld_con_data_flow_set = 0x4000109c
                0x400010a0                        r_lld_con_data_len_update = 0x400010a0
                0x400010a4                        r_lld_con_data_tx = 0x400010a4
                0x400010a8                        r_lld_con_enc_key_load = 0x400010a8
                0x400010ac                        r_lld_con_event_counter_get = 0x400010ac
                0x400010b0                        r_lld_con_evt_canceled_cbk = 0x400010b0
                0x400010b4                        r_lld_con_evt_duration_min_get = 0x400010b4
                0x400010b8                        r_lld_con_evt_max_eff_time_cal = 0x400010b8
                0x400010bc                        r_lld_con_evt_sd_evt_time_get = 0x400010bc
                0x400010c0                        r_lld_con_evt_start_cbk = 0x400010c0
                0x400010c4                        r_lld_con_evt_time_update = 0x400010c4
                0x400010c8                        r_lld_con_free_all_tx_buf = 0x400010c8
                0x400010cc                        r_lld_con_frm_cbk = 0x400010cc
                0x400010d0                        r_lld_con_frm_isr = 0x400010d0
                0x400010d4                        r_lld_con_frm_skip_isr = 0x400010d4
                0x400010d8                        r_lld_con_init = 0x400010d8
                0x400010dc                        r_lld_con_llcp_tx = 0x400010dc
                0x400010e0                        r_lld_con_max_lat_calc = 0x400010e0
                0x400010e4                        r_lld_con_offset_get = 0x400010e4
                0x400010e8                        r_lld_con_param_update = 0x400010e8
                0x400010ec                        r_lld_con_phys_update = 0x400010ec
                0x400010f0                        r_lld_con_pref_slave_evt_dur_set = 0x400010f0
                0x400010f4                        r_lld_con_pref_slave_latency_set = 0x400010f4
                0x400010f8                        r_lld_con_rssi_get = 0x400010f8
                0x400010fc                        r_lld_con_rx = 0x400010fc
                0x40001104                        r_lld_con_rx_enc = 0x40001104
                0x40001108                        r_lld_con_rx_isr = 0x40001108
                0x4000110c                        r_lld_con_rx_link_info_check = 0x4000110c
                0x40001110                        r_lld_con_rx_llcp_check = 0x40001110
                0x40001114                        r_lld_con_rx_sync_time_update = 0x40001114
                0x4000111c                        r_lld_con_set_tx_power = 0x4000111c
                0x40001120                        r_lld_con_start = 0x40001120
                0x40001128                        r_lld_con_tx = 0x40001128
                0x4000112c                        r_lld_con_tx_enc = 0x4000112c
                0x40001130                        r_lld_con_tx_isr = 0x40001130
                0x40001134                        r_lld_con_tx_len_update = 0x40001134
                0x40001138                        r_lld_con_tx_len_update_for_intv = 0x40001138
                0x4000113c                        r_lld_con_tx_len_update_for_rate = 0x4000113c
                0x40001140                        r_lld_con_tx_prog = 0x40001140
                0x40001144                        r_lld_conn_dynamic_pti_process = 0x40001144
                0x40001148                        r_lld_continue_scan_rx_isr_end_process = 0x40001148
                0x4000114c                        r_lld_ext_scan_dynamic_pti_process = 0x4000114c
                0x40001150                        r_lld_hw_cca_end_isr = 0x40001150
                0x40001154                        r_lld_hw_cca_evt_handler = 0x40001154
                0x40001158                        r_lld_hw_cca_isr = 0x40001158
                0x4000115c                        r_lld_init_cal_anchor_point = 0x4000115c
                0x40001160                        r_lld_init_compute_winoffset = 0x40001160
                0x40001164                        r_lld_init_connect_req_pack = 0x40001164
                0x40001168                        r_lld_init_end = 0x40001168
                0x4000116c                        r_lld_init_evt_canceled_cbk = 0x4000116c
                0x40001170                        r_lld_init_evt_start_cbk = 0x40001170
                0x40001174                        r_lld_init_frm_cbk = 0x40001174
                0x40001178                        r_lld_init_frm_eof_isr = 0x40001178
                0x4000117c                        r_lld_init_frm_skip_isr = 0x4000117c
                0x40001180                        r_lld_init_init = 0x40001180
                0x40001184                        r_lld_init_process_pkt_rx = 0x40001184
                0x40001188                        r_lld_init_process_pkt_rx_adv_ext_ind = 0x40001188
                0x4000118c                        r_lld_init_process_pkt_rx_adv_ind_or_direct_ind = 0x4000118c
                0x40001190                        r_lld_init_process_pkt_rx_aux_connect_rsp = 0x40001190
                0x40001194                        r_lld_init_process_pkt_tx = 0x40001194
                0x40001198                        r_lld_init_process_pkt_tx_cal_con_timestamp = 0x40001198
                0x4000119c                        r_lld_init_sched = 0x4000119c
                0x400011a0                        r_lld_init_set_tx_power = 0x400011a0
                0x400011a4                        r_lld_init_start = 0x400011a4
                0x400011a8                        r_lld_init_stop = 0x400011a8
                0x400011ac                        r_lld_instant_proc_end = 0x400011ac
                0x400011b4                        r_lld_per_adv_ch_map_update = 0x400011b4
                0x400011b8                        r_lld_per_adv_chain_construct = 0x400011b8
                0x400011bc                        r_lld_per_adv_cleanup = 0x400011bc
                0x400011c0                        r_lld_per_adv_coex_env_reset = 0x400011c0
                0x400011c4                        r_lld_per_adv_data_set = 0x400011c4
                0x400011c8                        r_lld_per_adv_data_update = 0x400011c8
                0x400011cc                        r_lld_per_adv_dynamic_pti_process = 0x400011cc
                0x400011d0                        r_lld_per_adv_evt_canceled_cbk = 0x400011d0
                0x400011d4                        r_lld_per_adv_evt_start_cbk = 0x400011d4
                0x400011d8                        r_lld_per_adv_ext_pkt_prepare = 0x400011d8
                0x400011dc                        r_lld_per_adv_frm_cbk = 0x400011dc
                0x400011e0                        r_lld_per_adv_frm_isr = 0x400011e0
                0x400011e4                        r_lld_per_adv_frm_skip_isr = 0x400011e4
                0x400011e8                        r_lld_per_adv_init = 0x400011e8
                0x400011ec                        r_lld_per_adv_init_info_get = 0x400011ec
                0x400011f0                        r_lld_per_adv_list_add = 0x400011f0
                0x400011f4                        r_lld_per_adv_list_rem = 0x400011f4
                0x400011fc                        r_lld_per_adv_set_tx_power = 0x400011fc
                0x40001200                        r_lld_per_adv_start = 0x40001200
                0x40001204                        r_lld_per_adv_stop = 0x40001204
                0x40001208                        r_lld_per_adv_sync_info_get = 0x40001208
                0x4000120c                        r_lld_process_cca_data = 0x4000120c
                0x40001210                        r_lld_ral_search = 0x40001210
                0x40001214                        r_lld_read_clock = 0x40001214
                0x40001218                        r_lld_res_list_add = 0x40001218
                0x40001220                        r_lld_res_list_is_empty = 0x40001220
                0x40001224                        r_lld_res_list_local_rpa_get = 0x40001224
                0x40001228                        r_lld_res_list_peer_rpa_get = 0x40001228
                0x4000122c                        r_lld_res_list_peer_update = 0x4000122c
                0x40001238                        r_lld_reset_reg = 0x40001238
                0x4000123c                        r_lld_rpa_renew = 0x4000123c
                0x40001240                        r_lld_rpa_renew_evt_canceled_cbk = 0x40001240
                0x40001244                        r_lld_rpa_renew_evt_start_cbk = 0x40001244
                0x40001248                        r_lld_rpa_renew_instant_cbk = 0x40001248
                0x4000124c                        r_lld_rxdesc_check = 0x4000124c
                0x40001250                        r_lld_rxdesc_free = 0x40001250
                0x40001254                        r_lld_scan_create_sync = 0x40001254
                0x40001258                        r_lld_scan_create_sync_cancel = 0x40001258
                0x4000125c                        r_lld_scan_end = 0x4000125c
                0x40001260                        r_lld_scan_evt_canceled_cbk = 0x40001260
                0x40001264                        r_lld_scan_evt_start_cbk = 0x40001264
                0x40001268                        r_lld_scan_frm_cbk = 0x40001268
                0x4000126c                        r_lld_scan_frm_eof_isr = 0x4000126c
                0x40001270                        r_lld_scan_frm_rx_isr = 0x40001270
                0x40001274                        r_lld_scan_frm_skip_isr = 0x40001274
                0x40001278                        r_lld_scan_init = 0x40001278
                0x4000127c                        r_lld_scan_params_update = 0x4000127c
                0x40001288                        r_lld_scan_process_pkt_rx_aux_adv_ind = 0x40001288
                0x4000128c                        r_lld_scan_process_pkt_rx_aux_chain_ind = 0x4000128c
                0x40001290                        r_lld_scan_process_pkt_rx_aux_scan_rsp = 0x40001290
                0x40001294                        r_lld_scan_process_pkt_rx_ext_adv = 0x40001294
                0x40001298                        r_lld_scan_process_pkt_rx_ext_adv_ind = 0x40001298
                0x4000129c                        r_lld_scan_process_pkt_rx_legacy_adv = 0x4000129c
                0x400012a0                        r_lld_scan_restart = 0x400012a0
                0x400012a4                        r_lld_scan_sched = 0x400012a4
                0x400012a8                        r_lld_scan_set_tx_power = 0x400012a8
                0x400012ac                        r_lld_scan_start = 0x400012ac
                0x400012b0                        r_lld_scan_stop = 0x400012b0
                0x400012b4                        r_lld_scan_sync_accept = 0x400012b4
                0x400012b8                        r_lld_scan_sync_info_unpack = 0x400012b8
                0x400012bc                        r_lld_scan_trunc_ind = 0x400012bc
                0x400012c0                        r_lld_sw_cca_evt_handler = 0x400012c0
                0x400012c4                        r_lld_sw_cca_isr = 0x400012c4
                0x400012c8                        r_lld_sync_ch_map_update = 0x400012c8
                0x400012cc                        r_lld_sync_cleanup = 0x400012cc
                0x400012d0                        r_lld_sync_evt_canceled_cbk = 0x400012d0
                0x400012d4                        r_lld_sync_evt_start_cbk = 0x400012d4
                0x400012d8                        r_lld_sync_frm_cbk = 0x400012d8
                0x400012dc                        r_lld_sync_frm_eof_isr = 0x400012dc
                0x400012e0                        r_lld_sync_frm_rx_isr = 0x400012e0
                0x400012e4                        r_lld_sync_frm_skip_isr = 0x400012e4
                0x400012e8                        r_lld_sync_init = 0x400012e8
                0x400012ec                        r_lld_sync_process_pkt_rx = 0x400012ec
                0x400012f0                        r_lld_sync_process_pkt_rx_aux_sync_ind = 0x400012f0
                0x400012f4                        r_lld_sync_process_pkt_rx_pkt_check = 0x400012f4
                0x400012f8                        r_lld_sync_scan_dynamic_pti_process = 0x400012f8
                0x400012fc                        r_lld_sync_sched = 0x400012fc
                0x40001300                        r_lld_sync_start = 0x40001300
                0x40001304                        r_lld_sync_stop = 0x40001304
                0x40001308                        r_lld_sync_trunc_ind = 0x40001308
                0x4000130c                        r_lld_test_cleanup = 0x4000130c
                0x40001310                        r_lld_test_evt_canceled_cbk = 0x40001310
                0x40001314                        r_lld_test_evt_start_cbk = 0x40001314
                0x40001318                        r_lld_test_freq2chnl = 0x40001318
                0x4000131c                        r_lld_test_frm_cbk = 0x4000131c
                0x40001320                        r_lld_test_frm_isr = 0x40001320
                0x40001324                        r_lld_test_init = 0x40001324
                0x40001328                        r_lld_test_rx_isr = 0x40001328
                0x4000132c                        r_lld_test_set_tx_power = 0x4000132c
                0x40001330                        r_lld_test_start = 0x40001330
                0x40001338                        r_lld_update_rxbuf = 0x40001338
                0x4000133c                        r_lld_update_rxbuf_isr = 0x4000133c
                0x40001340                        r_lld_white_list_add = 0x40001340
                0x40001344                        r_lld_white_list_rem = 0x40001344
                0x40001348                        r_llm_activity_free_get = 0x40001348
                0x4000134c                        r_llm_activity_free_set = 0x4000134c
                0x40001350                        r_llm_activity_syncing_get = 0x40001350
                0x40001354                        r_llm_adv_con_len_check = 0x40001354
                0x40001358                        r_llm_adv_hdl_to_id = 0x40001358
                0x4000135c                        r_llm_adv_rep_flow_control_check = 0x4000135c
                0x40001360                        r_llm_adv_rep_flow_control_update = 0x40001360
                0x40001364                        r_llm_adv_reports_list_check = 0x40001364
                0x40001368                        r_llm_adv_set_all_release = 0x40001368
                0x4000136c                        r_llm_adv_set_dft_params = 0x4000136c
                0x40001370                        r_llm_adv_set_release = 0x40001370
                0x40001374                        r_llm_aes_res_cb = 0x40001374
                0x40001378                        r_llm_ble_update_adv_flow_control = 0x40001378
                0x4000137c                        r_llm_ch_map_update = 0x4000137c
                0x40001380                        r_llm_cmd_cmp_send = 0x40001380
                0x40001384                        r_llm_cmd_stat_send = 0x40001384
                0x40001388                        r_llm_dev_list_empty_entry = 0x40001388
                0x4000138c                        r_llm_dev_list_search = 0x4000138c
                0x40001390                        r_llm_env_adv_dup_filt_deinit = 0x40001390
                0x40001394                        r_llm_env_adv_dup_filt_init = 0x40001394
                0x40001398                        r_llm_init_ble_adv_report_flow_contol = 0x40001398
                0x4000139c                        r_llm_is_dev_connected = 0x4000139c
                0x400013a0                        r_llm_is_dev_synced = 0x400013a0
                0x400013a4                        r_llm_is_non_con_act_ongoing_check = 0x400013a4
                0x400013a8                        r_llm_is_wl_accessible = 0x400013a8
                0x400013ac                        r_llm_le_evt_mask_check = 0x400013ac
                0x400013b4                        r_llm_link_disc = 0x400013b4
                0x400013b8                        r_llm_master_ch_map_get = 0x400013b8
                0x400013bc                        r_llm_msg_handler_tab_p_get = 0x400013bc
                0x400013c0                        r_llm_no_activity = 0x400013c0
                0x400013c4                        r_llm_per_adv_slot_dur = 0x400013c4
                0x400013c8                        r_llm_plan_elt_get = 0x400013c8
                0x400013cc                        r_llm_rx_path_comp_get = 0x400013cc
                0x400013d0                        r_llm_scan_start = 0x400013d0
                0x400013d4                        r_llm_scan_sync_acad_attach = 0x400013d4
                0x400013d8                        r_llm_scan_sync_acad_detach = 0x400013d8
                0x400013dc                        r_llm_send_adv_lost_event_to_host = 0x400013dc
                0x400013e0                        r_llm_tx_path_comp_get = 0x400013e0
                0x400013e4                        r_misc_deinit = 0x400013e4
                0x400013e8                        r_misc_free_em_buf_in_isr = 0x400013e8
                0x400013ec                        r_misc_init = 0x400013ec
                0x400013f0                        r_misc_msg_handler_tab_p_get = 0x400013f0
                0x400013f4                        r_notEqual256 = 0x400013f4
                0x400013f8                        r_phy_upd_proc_start = 0x400013f8
                0x400013fc                        r_platform_reset = 0x400013fc
                0x40001404                        r_rf_em_init = 0x40001404
                0x40001408                        r_rf_force_agc_enable = 0x40001408
                0x4000140c                        r_rf_reg_rd = 0x4000140c
                0x40001410                        r_rf_reg_wr = 0x40001410
                0x40001414                        r_rf_reset = 0x40001414
                0x40001418                        r_rf_rssi_convert = 0x40001418
                0x4000141c                        r_rf_rw_v9_le_disable = 0x4000141c
                0x40001420                        r_rf_rw_v9_le_enable = 0x40001420
                0x40001424                        r_rf_sleep = 0x40001424
                0x40001430                        r_rf_util_cs_fmt_convert = 0x40001430
                0x40001434                        r_rw_crypto_aes_ccm = 0x40001434
                0x40001438                        r_rw_crypto_aes_encrypt = 0x40001438
                0x4000143c                        r_rw_crypto_aes_init = 0x4000143c
                0x40001440                        r_rw_crypto_aes_k1 = 0x40001440
                0x40001444                        r_rw_crypto_aes_k2 = 0x40001444
                0x40001448                        r_rw_crypto_aes_k3 = 0x40001448
                0x4000144c                        r_rw_crypto_aes_k4 = 0x4000144c
                0x40001450                        r_rw_crypto_aes_rand = 0x40001450
                0x40001454                        r_rw_crypto_aes_result_handler = 0x40001454
                0x40001458                        r_rw_crypto_aes_s1 = 0x40001458
                0x4000145c                        r_rw_cryto_aes_cmac = 0x4000145c
                0x40001460                        r_rw_v9_init_em_radio_table = 0x40001460
                0x40001468                        r_rwble_sleep_enter = 0x40001468
                0x4000146c                        r_rwble_sleep_wakeup_end = 0x4000146c
                0x40001474                        r_rwip_active_check = 0x40001474
                0x40001478                        r_rwip_aes_encrypt = 0x40001478
                0x40001480                        r_rwip_crypt_evt_handler = 0x40001480
                0x40001484                        r_rwip_crypt_isr_handler = 0x40001484
                0x40001488                        r_rwip_eif_get = 0x40001488
                0x4000148c                        r_rwip_half_slot_2_lpcycles = 0x4000148c
                0x40001490                        r_rwip_hus_2_lpcycles = 0x40001490
                0x40001494                        r_rwip_isr = 0x40001494
                0x40001498                        r_rwip_lpcycles_2_hus = 0x40001498
                0x4000149c                        r_rwip_prevent_sleep_clear = 0x4000149c
                0x400014a0                        r_rwip_prevent_sleep_set = 0x400014a0
                0x400014a4                        r_rwip_schedule = 0x400014a4
                0x400014a8                        r_rwip_sleep = 0x400014a8
                0x400014ac                        r_rwip_sw_int_handler = 0x400014ac
                0x400014b0                        r_rwip_sw_int_req = 0x400014b0
                0x400014b4                        r_rwip_time_get = 0x400014b4
                0x400014b8                        r_rwip_timer_10ms_handler = 0x400014b8
                0x400014bc                        r_rwip_timer_10ms_set = 0x400014bc
                0x400014c0                        r_rwip_timer_hs_handler = 0x400014c0
                0x400014c4                        r_rwip_timer_hs_set = 0x400014c4
                0x400014c8                        r_rwip_timer_hus_handler = 0x400014c8
                0x400014cc                        r_rwip_timer_hus_set = 0x400014cc
                0x400014d0                        r_rwip_wakeup = 0x400014d0
                0x400014d8                        r_rwip_wlcoex_set = 0x400014d8
                0x400014dc                        r_sch_alarm_clear = 0x400014dc
                0x400014e0                        r_sch_alarm_init = 0x400014e0
                0x400014e4                        r_sch_alarm_prog = 0x400014e4
                0x400014e8                        r_sch_alarm_set = 0x400014e8
                0x400014ec                        r_sch_alarm_timer_isr = 0x400014ec
                0x400014f0                        r_sch_arb_conflict_check = 0x400014f0
                0x400014f4                        r_sch_arb_elt_cancel = 0x400014f4
                0x400014fc                        r_sch_arb_init = 0x400014fc
                0x40001500                        r_sch_arb_insert = 0x40001500
                0x40001504                        r_sch_arb_prog_timer = 0x40001504
                0x40001508                        r_sch_arb_remove = 0x40001508
                0x4000150c                        r_sch_arb_sw_isr = 0x4000150c
                0x40001510                        r_sch_plan_chk = 0x40001510
                0x40001514                        r_sch_plan_clock_wrap_offset_update = 0x40001514
                0x40001518                        r_sch_plan_init = 0x40001518
                0x4000151c                        r_sch_plan_interval_req = 0x4000151c
                0x40001520                        r_sch_plan_offset_max_calc = 0x40001520
                0x40001524                        r_sch_plan_offset_req = 0x40001524
                0x40001528                        r_sch_plan_position_range_compute = 0x40001528
                0x4000152c                        r_sch_plan_rem = 0x4000152c
                0x40001530                        r_sch_plan_req = 0x40001530
                0x4000153c                        r_sch_prog_init = 0x4000153c
                0x40001540                        r_sch_prog_push = 0x40001540
                0x40001544                        r_sch_prog_rx_isr = 0x40001544
                0x40001548                        r_sch_prog_skip_isr = 0x40001548
                0x4000154c                        r_sch_prog_tx_isr = 0x4000154c
                0x40001550                        r_sch_slice_bg_add = 0x40001550
                0x40001554                        r_sch_slice_bg_remove = 0x40001554
                0x40001558                        r_sch_slice_compute = 0x40001558
                0x4000155c                        r_sch_slice_fg_add = 0x4000155c
                0x40001560                        r_sch_slice_fg_remove = 0x40001560
                0x40001564                        r_sch_slice_init = 0x40001564
                0x40001568                        r_sch_slice_per_add = 0x40001568
                0x4000156c                        r_sch_slice_per_remove = 0x4000156c
                0x40001570                        r_sdk_config_get_bt_sleep_enable = 0x40001570
                0x40001574                        r_sdk_config_get_hl_derived_opts = 0x40001574
                0x40001578                        r_sdk_config_get_opts = 0x40001578
                0x4000157c                        r_sdk_config_get_priv_opts = 0x4000157c
                0x40001580                        r_sdk_config_set_bt_sleep_enable = 0x40001580
                0x40001584                        r_sdk_config_set_hl_derived_opts = 0x40001584
                0x40001588                        r_sdk_config_set_opts = 0x40001588
                0x4000158c                        r_specialModP256 = 0x4000158c
                0x40001590                        r_unloaded_area_init = 0x40001590
                0x40001594                        r_vhci_flow_off = 0x40001594
                0x40001598                        r_vhci_flow_on = 0x40001598
                0x4000159c                        r_vhci_notify_host_send_available = 0x4000159c
                0x400015a0                        r_vhci_send_to_host = 0x400015a0
                0x400015a4                        r_vnd_hci_command_handler = 0x400015a4
                0x400015a8                        r_vshci_init = 0x400015a8
                0x400015ac                        vnd_hci_command_handler_wrapper = 0x400015ac
                0x40001c60                        r_llc_loc_encrypt_proc_continue_hook = 0x40001c60
                0x40001c64                        r_llc_loc_phy_upd_proc_continue_hook = 0x40001c64
                0x40001c68                        r_llc_rem_phy_upd_proc_continue_hook = 0x40001c68
                0x40001c6c                        r_lld_scan_frm_eof_isr_hook = 0x40001c6c
                0x40001c70                        r_lld_scan_evt_start_cbk_hook = 0x40001c70
                0x40001c78                        r_lld_scan_process_pkt_rx_ext_adv_hook = 0x40001c78
                0x40001c7c                        r_lld_scan_sched_hook = 0x40001c7c
                0x40001c84                        r_lld_adv_evt_start_cbk_hook = 0x40001c84
                0x40001c88                        r_lld_adv_aux_evt_start_cbk_hook = 0x40001c88
                0x40001c8c                        r_lld_adv_frm_isr_hook = 0x40001c8c
                0x40001c90                        r_lld_adv_start_init_evt_param_hook = 0x40001c90
                0x40001c94                        r_lld_con_evt_canceled_cbk_hook = 0x40001c94
                0x40001c98                        r_lld_con_frm_isr_hook = 0x40001c98
                0x40001c9c                        r_lld_con_tx_hook = 0x40001c9c
                0x40001ca0                        r_lld_con_rx_hook = 0x40001ca0
                0x40001ca4                        r_lld_con_evt_start_cbk_hook = 0x40001ca4
                0x40001cac                        r_lld_con_tx_prog_new_packet_hook = 0x40001cac
                0x40001cb0                        r_lld_init_frm_eof_isr_hook = 0x40001cb0
                0x40001cb4                        r_lld_init_evt_start_cbk_hook = 0x40001cb4
                0x40001cbc                        r_lld_init_sched_hook = 0x40001cbc
                0x40001cc0                        r_lld_init_process_pkt_tx_hook = 0x40001cc0
                0x40001cc4                        r_lld_per_adv_evt_start_cbk_hook = 0x40001cc4
                0x40001cc8                        r_lld_per_adv_frm_isr_hook = 0x40001cc8
                0x40001ccc                        r_lld_per_adv_start_hook = 0x40001ccc
                0x40001cd0                        r_lld_sync_frm_eof_isr_hook = 0x40001cd0
                0x40001cd4                        r_lld_sync_evt_start_cbk_hook = 0x40001cd4
                0x40001cd8                        r_lld_sync_start_hook = 0x40001cd8
                0x40001cdc                        r_lld_sync_process_pkt_rx_pkt_check_hook = 0x40001cdc
                0x40001ce0                        r_sch_arb_insert_hook = 0x40001ce0
                0x40001ce4                        r_sch_plan_offset_req_hook = 0x40001ce4
                0x40000764                        __absvdi2 = 0x40000764
                0x40000768                        __absvsi2 = 0x40000768
                0x4000076c                        __adddf3 = 0x4000076c
                0x40000770                        __addsf3 = 0x40000770
                0x40000774                        __addvdi3 = 0x40000774
                0x40000778                        __addvsi3 = 0x40000778
                0x4000077c                        __ashldi3 = 0x4000077c
                0x40000780                        __ashrdi3 = 0x40000780
                0x40000784                        __bswapdi2 = 0x40000784
                0x40000788                        __bswapsi2 = 0x40000788
                0x4000078c                        __clear_cache = 0x4000078c
                0x40000790                        __clrsbdi2 = 0x40000790
                0x40000794                        __clrsbsi2 = 0x40000794
                0x40000798                        __clzdi2 = 0x40000798
                0x4000079c                        __clzsi2 = 0x4000079c
                0x400007a0                        __cmpdi2 = 0x400007a0
                0x400007a4                        __ctzdi2 = 0x400007a4
                0x400007a8                        __ctzsi2 = 0x400007a8
                0x400007ac                        __divdc3 = 0x400007ac
                0x400007b0                        __divdf3 = 0x400007b0
                0x400007b4                        __divdi3 = 0x400007b4
                0x400007b8                        __divsc3 = 0x400007b8
                0x400007bc                        __divsf3 = 0x400007bc
                0x400007c0                        __divsi3 = 0x400007c0
                0x400007c4                        __eqdf2 = 0x400007c4
                0x400007c8                        __eqsf2 = 0x400007c8
                0x400007cc                        __extendsfdf2 = 0x400007cc
                0x400007d0                        __ffsdi2 = 0x400007d0
                0x400007d4                        __ffssi2 = 0x400007d4
                0x400007d8                        __fixdfdi = 0x400007d8
                0x400007dc                        __fixdfsi = 0x400007dc
                0x400007e0                        __fixsfdi = 0x400007e0
                0x400007e4                        __fixsfsi = 0x400007e4
                0x400007e8                        __fixunsdfsi = 0x400007e8
                0x400007ec                        __fixunssfdi = 0x400007ec
                0x400007f0                        __fixunssfsi = 0x400007f0
                0x400007f4                        __floatdidf = 0x400007f4
                0x400007f8                        __floatdisf = 0x400007f8
                0x400007fc                        __floatsidf = 0x400007fc
                0x40000800                        __floatsisf = 0x40000800
                0x40000804                        __floatundidf = 0x40000804
                0x40000808                        __floatundisf = 0x40000808
                0x4000080c                        __floatunsidf = 0x4000080c
                0x40000810                        __floatunsisf = 0x40000810
                0x40000814                        __gcc_bcmp = 0x40000814
                0x40000818                        __gedf2 = 0x40000818
                0x4000081c                        __gesf2 = 0x4000081c
                0x40000820                        __gtdf2 = 0x40000820
                0x40000824                        __gtsf2 = 0x40000824
                0x40000828                        __ledf2 = 0x40000828
                0x4000082c                        __lesf2 = 0x4000082c
                0x40000830                        __lshrdi3 = 0x40000830
                0x40000834                        __ltdf2 = 0x40000834
                0x40000838                        __ltsf2 = 0x40000838
                0x4000083c                        __moddi3 = 0x4000083c
                0x40000840                        __modsi3 = 0x40000840
                0x40000844                        __muldc3 = 0x40000844
                0x40000848                        __muldf3 = 0x40000848
                0x4000084c                        __muldi3 = 0x4000084c
                0x40000850                        __mulsc3 = 0x40000850
                0x40000854                        __mulsf3 = 0x40000854
                0x40000858                        __mulsi3 = 0x40000858
                0x4000085c                        __mulvdi3 = 0x4000085c
                0x40000860                        __mulvsi3 = 0x40000860
                0x40000864                        __nedf2 = 0x40000864
                0x40000868                        __negdf2 = 0x40000868
                0x4000086c                        __negdi2 = 0x4000086c
                0x40000870                        __negsf2 = 0x40000870
                0x40000874                        __negvdi2 = 0x40000874
                0x40000878                        __negvsi2 = 0x40000878
                0x4000087c                        __nesf2 = 0x4000087c
                0x40000880                        __paritysi2 = 0x40000880
                0x40000884                        __popcountdi2 = 0x40000884
                0x40000888                        __popcountsi2 = 0x40000888
                0x4000088c                        __powidf2 = 0x4000088c
                0x40000890                        __powisf2 = 0x40000890
                0x40000894                        __subdf3 = 0x40000894
                0x40000898                        __subsf3 = 0x40000898
                0x4000089c                        __subvdi3 = 0x4000089c
                0x400008a0                        __subvsi3 = 0x400008a0
                0x400008a4                        __truncdfsf2 = 0x400008a4
                0x400008a8                        __ucmpdi2 = 0x400008a8
                0x400008ac                        __udivdi3 = 0x400008ac
                0x400008b0                        __udivmoddi4 = 0x400008b0
                0x400008b4                        __udivsi3 = 0x400008b4
                0x400008b8                        __udiv_w_sdiv = 0x400008b8
                0x400008bc                        __umoddi3 = 0x400008bc
                0x400008c0                        __umodsi3 = 0x400008c0
                0x400008c4                        __unorddf2 = 0x400008c4
                0x400008c8                        __unordsf2 = 0x400008c8
                0x40000010                        _rom_chip_id = 0x40000010
                0x40000014                        _rom_eco_version = 0x40000014
                0x40000350                        esp_rom_newlib_init_common_mutexes = 0x40000350
                0x40000354                        memset = 0x40000354
                0x40000358                        memcpy = 0x40000358
                0x4000035c                        memmove = 0x4000035c
                0x40000360                        memcmp = 0x40000360
                0x40000364                        strcpy = 0x40000364
                0x40000368                        strncpy = 0x40000368
                0x4000036c                        strcmp = 0x4000036c
                0x40000370                        strncmp = 0x40000370
                0x40000374                        strlen = 0x40000374
                0x40000378                        strstr = 0x40000378
                0x4000037c                        bzero = 0x4000037c
                0x40000384                        sbrk = 0x40000384
                0x40000388                        isalnum = 0x40000388
                0x4000038c                        isalpha = 0x4000038c
                0x40000390                        isascii = 0x40000390
                0x40000394                        isblank = 0x40000394
                0x40000398                        iscntrl = 0x40000398
                0x4000039c                        isdigit = 0x4000039c
                0x400003a0                        islower = 0x400003a0
                0x400003a4                        isgraph = 0x400003a4
                0x400003a8                        isprint = 0x400003a8
                0x400003ac                        ispunct = 0x400003ac
                0x400003b0                        isspace = 0x400003b0
                0x400003b4                        isupper = 0x400003b4
                0x400003b8                        toupper = 0x400003b8
                0x400003bc                        tolower = 0x400003bc
                0x400003c0                        toascii = 0x400003c0
                0x400003c4                        memccpy = 0x400003c4
                0x400003c8                        memchr = 0x400003c8
                0x400003cc                        memrchr = 0x400003cc
                0x400003d0                        strcasecmp = 0x400003d0
                0x400003d4                        strcasestr = 0x400003d4
                0x400003d8                        strcat = 0x400003d8
                0x400003dc                        strdup = 0x400003dc
                0x400003e0                        strchr = 0x400003e0
                0x400003e4                        strcspn = 0x400003e4
                0x400003e8                        strcoll = 0x400003e8
                0x400003ec                        strlcat = 0x400003ec
                0x400003f0                        strlcpy = 0x400003f0
                0x400003f4                        strlwr = 0x400003f4
                0x400003f8                        strncasecmp = 0x400003f8
                0x400003fc                        strncat = 0x400003fc
                0x40000400                        strndup = 0x40000400
                0x40000404                        strnlen = 0x40000404
                0x40000408                        strrchr = 0x40000408
                0x4000040c                        strsep = 0x4000040c
                0x40000410                        strspn = 0x40000410
                0x40000414                        strtok_r = 0x40000414
                0x40000418                        strupr = 0x40000418
                0x4000041c                        longjmp = 0x4000041c
                0x40000420                        setjmp = 0x40000420
                0x40000424                        abs = 0x40000424
                0x40000428                        div = 0x40000428
                0x4000042c                        labs = 0x4000042c
                0x40000430                        ldiv = 0x40000430
                0x40000434                        qsort = 0x40000434
                0x40000438                        rand_r = 0x40000438
                0x4000043c                        rand = 0x4000043c
                0x40000440                        srand = 0x40000440
                0x40000444                        utoa = 0x40000444
                0x40000448                        itoa = 0x40000448
                0x4000044c                        atoi = 0x4000044c
                0x40000450                        atol = 0x40000450
                0x40000454                        strtol = 0x40000454
                0x40000458                        strtoul = 0x40000458
                [!provide]                        PROVIDE (fflush = 0x4000045c)
                [!provide]                        PROVIDE (_fflush_r = 0x40000460)
                [!provide]                        PROVIDE (_fwalk = 0x40000464)
                [!provide]                        PROVIDE (_fwalk_reent = 0x40000468)
                [!provide]                        PROVIDE (__swbuf_r = 0x40000474)
                0x40000478                        __swbuf = 0x40000478
                0x3fcdffe0                        syscall_table_ptr = 0x3fcdffe0
                0x3fcdffdc                        _global_impure_ptr = 0x3fcdffdc
                [!provide]                        PROVIDE (UART0 = 0x60000000)
                0x60010000                        PROVIDE (UART1 = 0x60010000)
                0x60002000                        PROVIDE (SPIMEM1 = 0x60002000)
                0x60003000                        PROVIDE (SPIMEM0 = 0x60003000)
                [!provide]                        PROVIDE (GPIO = 0x60004000)
                [!provide]                        PROVIDE (SDM = 0x60004f00)
                0x60008000                        PROVIDE (RTCCNTL = 0x60008000)
                [!provide]                        PROVIDE (RTCIO = 0x60008400)
                0x60008800                        PROVIDE (EFUSE = 0x60008800)
                [!provide]                        PROVIDE (HINF = 0x6000b000)
                [!provide]                        PROVIDE (I2S0 = 0x6002d000)
                [!provide]                        PROVIDE (I2C0 = 0x60013000)
                [!provide]                        PROVIDE (UHCI0 = 0x60014000)
                [!provide]                        PROVIDE (HOST = 0x60015000)
                [!provide]                        PROVIDE (RMT = 0x60016000)
                [!provide]                        PROVIDE (RMTMEM = 0x60016400)
                [!provide]                        PROVIDE (SLC = 0x60018000)
                [!provide]                        PROVIDE (LEDC = 0x60019000)
                0x6001f000                        PROVIDE (TIMERG0 = 0x6001f000)
                0x60020000                        PROVIDE (TIMERG1 = 0x60020000)
                [!provide]                        PROVIDE (SYSTIMER = 0x60023000)
                [!provide]                        PROVIDE (GPSPI2 = 0x60024000)
                [!provide]                        PROVIDE (SYSCON = 0x60026000)
                [!provide]                        PROVIDE (TWAI = 0x6002b000)
                [!provide]                        PROVIDE (APB_SARADC = 0x60040000)
                [!provide]                        PROVIDE (USB_SERIAL_JTAG = 0x60043000)
                [!provide]                        PROVIDE (GDMA = 0x6003f000)
                0x600c0000                        PROVIDE (SYSTEM = 0x600c0000)
                0x00700000                        iram_dram_offset = 0x700000
                0x3fcdc710                        bootloader_usable_dram_end = 0x3fcdc710
                0x00002000                        bootloader_stack_overhead = 0x2000
                0x00005000                        bootloader_dram_seg_len = 0x5000
                0x00007000                        bootloader_iram_loader_seg_len = 0x7000
                0x00002000                        bootloader_iram_seg_len = 0x2000
                0x3fcda710                        bootloader_dram_seg_end = (bootloader_usable_dram_end - bootloader_stack_overhead)
                0x3fcd5710                        bootloader_dram_seg_start = (bootloader_dram_seg_end - bootloader_dram_seg_len)
                0x403ce710                        bootloader_iram_loader_seg_start = ((bootloader_dram_seg_start - bootloader_iram_loader_seg_len) + iram_dram_offset)
                0x403cc710                        bootloader_iram_seg_start = (bootloader_iram_loader_seg_start - bootloader_iram_seg_len)
                0x00000001                        ASSERT ((bootloader_iram_loader_seg_start == 0x403ce710), bootloader_iram_loader_seg_start inconsistent with SRAM_DRAM_END)

.iram_loader.text
                0x403ce710     0x2f96
                0x403ce710                        . = ALIGN (0x10)
                0x403ce710                        _loader_text_start = ABSOLUTE (.)
 *(.stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 *(.iram1 .iram1.*)
 .iram1.0       0x403ce710        0x2 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                0x403ce710                esp_flash_encryption_enabled
 .iram1.5       0x403ce712       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.1       0x403ce762      0x1b0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403ce762                bootloader_flash_execute_command_common
 .iram1.2       0x403ce912        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403ce912                bootloader_execute_flash_command
 .iram1.0       0x403ce920      0x15a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403ce920                bootloader_flash_unlock
 .iram1.3       0x403cea7a       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cea7a                bootloader_flash_read_sfdp
 .iram1.4       0x403ceaba       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403ceaba                bootloader_read_flash_id
 .iram1.6       0x403ceaee       0xbe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403ceaee                bootloader_flash_xmc_startup
 .iram1.0       0x403cebac       0x42 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                0x403cebac                bootloader_flash_cs_timing_config
 .iram1.2       0x403cebee       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                0x403cebee                bootloader_flash_set_dummy_out
 .iram1.4       0x403cec0a       0xce esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                0x403cec0a                bootloader_configure_spi_pins
 .iram1.1       0x403cecd8       0x1a esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
                0x403cecd8                esp_rom_install_uart_printf
 .iram1.0       0x403cecf2       0x3c esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
                0x403cecf2                esp_rom_output_set_as_console
 .iram1.0       0x403ced2e       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403ced2e                efuse_hal_chip_revision
 .iram1.1       0x403ced4c       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403ced4c                efuse_hal_blk_version
 .iram1.2       0x403ced6a        0xe esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403ced6a                efuse_hal_get_disable_wafer_version_major
 .iram1.3       0x403ced78       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403ced78                efuse_hal_get_disable_blk_version_major
 .iram1.4       0x403ced88       0x22 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403ced88                efuse_hal_flash_encryption_enabled
 .iram1.0       0x403cedaa       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403cedaa                efuse_hal_get_major_chip_version
 .iram1.1       0x403cedba       0x18 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403cedba                efuse_hal_get_minor_chip_version
 *liblog.a:(.literal .text .literal.* .text.*)
 .text.esp_log_early_timestamp
                0x403cedd2       0x26 esp-idf/log/liblog.a(log_noos.c.obj)
                0x403cedd2                esp_log_early_timestamp
                0x403cedd2                esp_log_timestamp
 *libgcc.a:(.literal .text .literal.* .text.*)
 *libclang_rt.builtins.a:(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_clock_loader.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_common_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_common_ota_select_crc
                0x403cedf8        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403cedf8                bootloader_common_ota_select_crc
 .text.bootloader_common_ota_select_invalid
                0x403cee06       0x16 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403cee06                bootloader_common_ota_select_invalid
 .text.bootloader_common_ota_select_valid
                0x403cee1c       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403cee1c                bootloader_common_ota_select_valid
 .text.bootloader_common_check_efuse_blk_validity
                0x403cee46       0xb4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403cee46                bootloader_common_check_efuse_blk_validity
 .text.bootloader_common_check_chip_validity
                0x403ceefa      0x118 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403ceefa                bootloader_common_check_chip_validity
 .text.bootloader_common_select_otadata
                0x403cf012       0x3a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403cf012                bootloader_common_select_otadata
 .text.bootloader_common_get_active_otadata
                0x403cf04c       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403cf04c                bootloader_common_get_active_otadata
 *libbootloader_support.a:bootloader_flash.*(.literal .text .literal.* .text.*)
 .text.spi_to_esp_err
                0x403cf07a       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_mmap_get_free_pages
                0x403cf09c        0x6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf09c                bootloader_mmap_get_free_pages
 .text.bootloader_mmap
                0x403cf0a2       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf0a2                bootloader_mmap
 .text.bootloader_munmap
                0x403cf16a       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf16a                bootloader_munmap
 .text.bootloader_flash_read
                0x403cf19c      0x138 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf19c                bootloader_flash_read
 .text.bootloader_flash_erase_sector
                0x403cf2d4       0x12 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf2d4                bootloader_flash_erase_sector
 .text.bootloader_flash_write
                0x403cf2e6       0xca esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf2e6                bootloader_flash_write
 .text.bootloader_enable_wp
                0x403cf3b0        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf3b0                bootloader_enable_wp
 .text.bootloader_flash_get_spi_mode
                0x403cf3bc       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403cf3bc                bootloader_flash_get_spi_mode
 *libbootloader_support.a:bootloader_random.*(.literal .text .literal.* .text.*)
 .text.bootloader_fill_random
                0x403cf3fa       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                0x403cf3fa                bootloader_fill_random
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_disable .text.bootloader_random_disable)
 .text.bootloader_random_disable
                0x403cf46a       0x86 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
                0x403cf46a                bootloader_random_disable
 *libbootloader_support.a:bootloader_efuse.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_utility.*(.literal .text .literal.* .text.*)
 .text.log_invalid_app_partition
                0x403cf4f0       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.index_to_partition
                0x403cf574       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.try_load_partition
                0x403cf5ba       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.cache_ll_l1_enable_bus.constprop.0
                0x403cf5fe       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.set_actual_ota_seq
                0x403cf62e       0xaa esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.load_image
                0x403cf6d8      0x1aa esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_read_otadata
                0x403cf882       0xa2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403cf882                bootloader_common_read_otadata
 .text.bootloader_utility_load_partition_table
                0x403cf924      0x234 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403cf924                bootloader_utility_load_partition_table
 .text.bootloader_utility_get_selected_boot_partition
                0x403cfb58      0x112 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403cfb58                bootloader_utility_get_selected_boot_partition
 .text.bootloader_reset
                0x403cfc6a       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403cfc6a                bootloader_reset
 .text.bootloader_utility_load_boot_image
                0x403cfc86      0x11c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403cfc86                bootloader_utility_load_boot_image
 .text.bootloader_debug_buffer
                0x403cfda2        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403cfda2                bootloader_debug_buffer
 *libbootloader_support.a:bootloader_sha.*(.literal .text .literal.* .text.*)
 .text.bootloader_sha256_start
                0x403cfda4       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x403cfda4                bootloader_sha256_start
 .text.bootloader_sha256_data
                0x403cfdd0       0x52 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x403cfdd0                bootloader_sha256_data
 .text.bootloader_sha256_finish
                0x403cfe22       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x403cfe22                bootloader_sha256_finish
 *libbootloader_support.a:bootloader_console_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_console_deinit
                0x403cfe68        0xa esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                0x403cfe68                bootloader_console_deinit
 *libbootloader_support.a:bootloader_panic.*(.literal .text .literal.* .text.*)
 .text.__assert_func
                0x403cfe72       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x403cfe72                __assert_func
 .text.unlikely.abort
                0x403cfe92       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x403cfe92                abort
 *libbootloader_support.a:bootloader_soc.*(.literal .text .literal.* .text.*)
 .text.bootloader_ana_super_wdt_reset_config
                0x403cfebc       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x403cfebc                bootloader_ana_super_wdt_reset_config
 .text.bootloader_ana_bod_reset_config
                0x403cfee8       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x403cfee8                bootloader_ana_bod_reset_config
 .text.bootloader_ana_clock_glitch_reset_config
                0x403cff16       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x403cff16                bootloader_ana_clock_glitch_reset_config
 *libbootloader_support.a:esp_image_format.*(.literal .text .literal.* .text.*)
 .text.bootloader_util_regions_overlap
                0x403cff44       0x4e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.should_load
                0x403cff92       0x5a esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.process_segments
                0x403cffec      0x4c6 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.image_load
                0x403d04b2      0x3f2 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image
                0x403d08a4        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                0x403d08a4                bootloader_load_image
 *libbootloader_support.a:flash_encrypt.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_encryption_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_partitions.*(.literal .text .literal.* .text.*)
 .text.esp_partition_table_verify
                0x403d08ac      0x17e esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                0x403d08ac                esp_partition_table_verify
 *libbootloader_support.a:secure_boot.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_signatures_bootloader.*(.literal .text .literal.* .text.*)
 *libmicro-ecc.a:*.*(.literal .text .literal.* .text.*)
 *libspi_flash.a:*.*(.literal .text .literal.* .text.*)
 *libhal.a:wdt_hal_iram.*(.literal .text .literal.* .text.*)
 .text.wdt_hal_init
                0x403d0a2a      0x1cc esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                0x403d0a2a                wdt_hal_init
 .text.wdt_hal_config_stage
                0x403d0bf6      0x10c esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                0x403d0bf6                wdt_hal_config_stage
 .text.wdt_hal_write_protect_disable
                0x403d0d02       0x18 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                0x403d0d02                wdt_hal_write_protect_disable
 .text.wdt_hal_write_protect_enable
                0x403d0d1a       0x12 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                0x403d0d1a                wdt_hal_write_protect_enable
 .text.wdt_hal_enable
                0x403d0d2c       0x2c esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                0x403d0d2c                wdt_hal_enable
 .text.wdt_hal_set_flashboot_en
                0x403d0d58       0x36 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                0x403d0d58                wdt_hal_set_flashboot_en
 *libhal.a:mmu_hal.*(.literal .text .literal.* .text.*)
 .text.mmu_hal_unmap_all
                0x403d0d8e       0x1a esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403d0d8e                mmu_hal_unmap_all
 .text.mmu_hal_init
                0x403d0da8       0x12 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403d0da8                mmu_hal_init
 .text.mmu_hal_check_valid_ext_vaddr_region
                0x403d0dba       0x48 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403d0dba                mmu_hal_check_valid_ext_vaddr_region
 .text.mmu_hal_map_region
                0x403d0e02       0x94 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403d0e02                mmu_hal_map_region
 *libhal.a:cache_hal.*(.literal .text .literal.* .text.*)
 .text.s_cache_hal_init_ctx
                0x403d0e96       0x3a esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403d0e96                s_cache_hal_init_ctx
 .text.cache_hal_init
                0x403d0ed0       0x36 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403d0ed0                cache_hal_init
 .text.cache_hal_disable
                0x403d0f06       0x16 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403d0f06                cache_hal_disable
 .text.cache_hal_enable
                0x403d0f1c       0x20 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403d0f1c                cache_hal_enable
 *libhal.a:efuse_hal.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:rtc_clk.*(.literal .text .literal.* .text.*)
 .text.rtc_clk_bbpll_disable
                0x403d0f3c       0x16 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_pll_mhz
                0x403d0f52       0x64 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable.part.0
                0x403d0fb6       0x56 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable
                0x403d100c       0x1c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d100c                rtc_clk_32k_enable
 .text.rtc_clk_8m_enable
                0x403d1028       0x76 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1028                rtc_clk_8m_enable
 .text.rtc_clk_slow_src_set
                0x403d109e       0x68 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d109e                rtc_clk_slow_src_set
 .text.rtc_clk_slow_src_get
                0x403d1106       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1106                rtc_clk_slow_src_get
 .text.rtc_clk_slow_freq_get_hz
                0x403d1120       0x2e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1120                rtc_clk_slow_freq_get_hz
 .text.rtc_clk_fast_src_set
                0x403d114e       0x38 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d114e                rtc_clk_fast_src_set
 .text.rtc_clk_fast_src_get
                0x403d1186        0xc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1186                rtc_clk_fast_src_get
 .text.rtc_clk_xtal_freq_get
                0x403d1192       0x58 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1192                rtc_clk_xtal_freq_get
                0x403d1192                rtc_get_xtal
 .text.rtc_clk_cpu_freq_mhz_to_config
                0x403d11ea       0x60 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d11ea                rtc_clk_cpu_freq_mhz_to_config
 .text.rtc_clk_cpu_freq_get_config
                0x403d124a       0xd6 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d124a                rtc_clk_cpu_freq_get_config
 .text.rtc_clk_xtal_freq_update
                0x403d1320       0x2e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1320                rtc_clk_xtal_freq_update
 .text.rtc_clk_apb_freq_update
                0x403d134e       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d134e                rtc_clk_apb_freq_update
 .text.rtc_clk_cpu_freq_to_xtal
                0x403d1366       0x5c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_config
                0x403d13c2      0x242 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d13c2                rtc_clk_cpu_freq_set_config
 .text.rtc_clk_apb_freq_get
                0x403d1604       0x2e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1604                rtc_clk_apb_freq_get
 .text.rtc_clk_divider_set
                0x403d1632       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1632                rtc_clk_divider_set
 .text.rtc_clk_8m_divider_set
                0x403d1672       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403d1672                rtc_clk_8m_divider_set
 *libesp_hw_support.a:rtc_time.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:regi2c_ctrl.*(.literal .text .literal.* .text.*)
 *libefuse.a:*.*(.literal .text .literal.* .text.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x403d16a6                        _loader_text_end = ABSOLUTE (.)

.iram.text      0x403cc710        0x0
                0x403cc710                        . = ALIGN (0x10)
 *(.entry.text)
 *(.init.literal)
 *(.init)

.dram0.bss      0x3fcd5710      0x110
                0x3fcd5710                        . = ALIGN (0x8)
                0x3fcd5710                        _dram_start = ABSOLUTE (.)
                0x3fcd5710                        _bss_start = ABSOLUTE (.)
 *(.dynsbss)
 *(.sbss)
 *(.sbss.*)
 .sbss.ota_has_initial_contents
                0x3fcd5710        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 *fill*         0x3fcd5711        0x3 
 .sbss.ram_obfs_value
                0x3fcd5714        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .sbss.mapped   0x3fcd571c        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x3fcd571d        0x3 
 .sbss.s_bbpll_digi_consumers_ref_count
                0x3fcd5720        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .sbss.s_cur_pll_freq
                0x3fcd5724        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .sbss.ctx      0x3fcd5728        0x4 esp-idf/hal/libhal.a(cache_hal.c.obj)
 *(.gnu.linkonce.sb.*)
 *(.scommon)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(.dynbss)
 *(.bss)
 *(.bss.*)
 .bss.ctx       0x3fcd572c       0xd8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss.bootloader_image_hdr
                0x3fcd5804       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x3fcd5804                bootloader_image_hdr
 *(.gnu.linkonce.b.*)
 *(COMMON)
                0x3fcd5820                        . = ALIGN (0x8)
 *fill*         0x3fcd581c        0x4 
                0x3fcd5820                        _bss_end = ABSOLUTE (.)

.dram0.bootdesc
                0x3fcd5820       0x50
                0x3fcd5820                        _data_start = ABSOLUTE (.)
 *(.data_bootloader_desc .data_bootloader_desc.*)
 .data_bootloader_desc
                0x3fcd5820       0x50 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x3fcd5820                esp_bootloader_desc

.dram0.data     0x3fcd5870        0x4
 *(.dram1 .dram1.*)
 *(.data)
 *(.data.*)
 *(.gnu.linkonce.d.*)
 *(.data1)
 *(.sdata)
 *(.sdata.*)
 .sdata.current_read_mapping
                0x3fcd5870        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *(.gnu.linkonce.s.*)
 *(.gnu.linkonce.s2.*)
 *(.jcr)
                0x3fcd5874                        _data_end = ABSOLUTE (.)

.dram0.rodata   0x3fcd5874     0x17f4
                0x3fcd5874                        _rodata_start = ABSOLUTE (.)
 *(.rodata)
 *(.rodata.*)
 .rodata.__assert_func.str1.4
                0x3fcd5874     0x16f4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                 0x22 (size before relaxing)
 .rodata.abort.str1.4
                0x3fcd6f68       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .rodata.call_start_cpu0.str1.4
                0x3fcd6f68       0x3c esp-idf/main/libmain.a(bootloader_start.c.obj)
 .rodata.log_invalid_app_partition.str1.4
                0x3fcd6f68       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.try_load_partition.str1.4
                0x3fcd6f68       0x42 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.set_actual_ota_seq.str1.4
                0x3fcd6f68       0x85 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.load_image.str1.4
                0x3fcd6f68       0xad esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_common_read_otadata.str1.4
                0x3fcd6f68       0x91 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_partition_table.str1.4
                0x3fcd6f68      0x1df esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_get_selected_boot_partition.str1.4
                0x3fcd6f68      0x113 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_boot_image.str1.4
                0x3fcd6f68       0xe2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.esp_partition_table_verify.str1.4
                0x3fcd6f68      0x16c esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .rodata.bootloader_util_regions_overlap.str1.4
                0x3fcd6f68       0x5e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.process_segments.str1.4
                0x3fcd6f68      0x2a9 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.image_load.str1.4
                0x3fcd6f68      0x1da esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.0
                0x3fcd6f68       0x20 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.1
                0x3fcd6f88       0x16 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.bootloader_sha256_data.str1.4
                0x3fcd6f9e       0x66 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x3fcd6f9e        0x2 
 .rodata.__func__.0
                0x3fcd6fa0       0x19 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x3fcd6fb9        0x3 
 .rodata.__func__.1
                0x3fcd6fbc       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .rodata.bootloader_init.str1.4
                0x3fcd6fd3      0x10f esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 *fill*         0x3fcd6fd3        0x1 
 .rodata.__func__.0
                0x3fcd6fd4       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .rodata.bootloader_common_check_efuse_blk_validity.str1.4
                0x3fcd6fe4       0xbb esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_common_check_chip_validity.str1.4
                0x3fcd6fe4       0xde esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_fill_random.str1.4
                0x3fcd6fe4       0x4c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.__func__.0
                0x3fcd6fe4       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.bootloader_mmap.str1.4
                0x3fcd6ffb       0xac esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_read.str1.4
                0x3fcd6ffb       0xe7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.str1.4
                0x3fcd6ffb       0xdf esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_write.str1.4
                0x3fcd6ffb       0xef esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x3fcd6ffb        0x1 
 .rodata.__func__.0
                0x3fcd6ffc       0x1b esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x3fcd7017        0x1 
 .rodata.__func__.1
                0x3fcd7018       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_init_spi_flash.str1.4
                0x3fcd7040       0xfc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .rodata.bootloader_read_bootloader_header.str1.4
                0x3fcd7040       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_check_bootloader_validity.str1.4
                0x3fcd7040       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_enable_random.str1.4
                0x3fcd7040       0x3d esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_print_banner.str1.4
                0x3fcd7040       0x60 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.rtc_clk_init.str1.4
                0x3fcd7040       0x44 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .rodata.rtc_clk_xtal_freq_get.str1.4
                0x3fcd7040       0x4e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.rtc_clk_cpu_freq_get_config.str1.4
                0x3fcd7040       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.uart_periph_signal
                0x3fcd7040       0x28 esp-idf/soc/libsoc.a(uart_periph.c.obj)
                0x3fcd7040                uart_periph_signal
 *(.gnu.linkonce.r.*)
 *(.rodata1)
 *(.sdata2 .sdata2.* .srodata .srodata.*)
                0x3fcd7068                        __XT_EXCEPTION_TABLE_ = ABSOLUTE (.)
 *(.xt_except_table)
 *(.gcc_except_table)
 *(.gnu.linkonce.e.*)
 *(.gnu.version_r)
 *(.eh_frame_hdr)
 *(.eh_frame)
                0x3fcd713c                        . = ((. + 0x3) & 0xfffffffffffffffc)
                0x3fcd7068                        __init_array_start = ABSOLUTE (.)
 *crtbegin.*(.ctors)
 *(EXCLUDE_FILE(*crtend.*) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
                0x3fcd7068                        __init_array_end = ABSOLUTE (.)
 *crtbegin.*(.dtors)
 *(EXCLUDE_FILE(*crtend.*) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
                0x3fcd7068                        __XT_EXCEPTION_DESCS_ = ABSOLUTE (.)
 *(.xt_except_desc)
 *(.gnu.linkonce.h.*)
                0x3fcd7068                        __XT_EXCEPTION_DESCS_END__ = ABSOLUTE (.)
 *(.xt_except_desc_end)
 *(.dynamic)
 *(.gnu.version_d)
                0x3fcd7068                        _rodata_end = ABSOLUTE (.)
                0x3fcd7068                        _lit4_start = ABSOLUTE (.)
 *(*.lit4)
 *(.lit4.*)
 *(.gnu.linkonce.lit4.*)
                0x3fcd7068                        _lit4_end = ABSOLUTE (.)
                0x3fcd713c                        . = ALIGN (0x4)
                0x3fcd7068                        _dram_end = ABSOLUTE (.)

.iram.text      0x403cc710      0xcc4
                0x403cc710                        _stext = .
                0x403cc710                        _text_start = ABSOLUTE (.)
 *(.literal .text .literal.* .text.* .stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 .text.esp_bootloader_get_description
                0x403cc710        0xa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x403cc710                esp_bootloader_get_description
 .text.call_start_cpu0
                0x403cc71a       0x82 esp-idf/main/libmain.a(bootloader_start.c.obj)
                0x403cc71a                call_start_cpu0
 .text.bootloader_init
                0x403cc79c      0x1aa esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                0x403cc79c                bootloader_init
 .text.bootloader_clock_configure
                0x403cc946       0xd6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                0x403cc946                bootloader_clock_configure
 .text.bootloader_init_mem
                0x403cca1c        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                0x403cca1c                bootloader_init_mem
 .text.bootloader_random_enable
                0x403cca20      0x1a2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
                0x403cca20                bootloader_random_enable
 .text.bootloader_flash_update_id
                0x403ccbc2       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                0x403ccbc2                bootloader_flash_update_id
 .text.bootloader_init_spi_flash
                0x403ccbde      0x1f2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                0x403ccbde                bootloader_init_spi_flash
 .text.bootloader_clear_bss_section
                0x403ccdd0       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ccdd0                bootloader_clear_bss_section
 .text.bootloader_read_bootloader_header
                0x403ccdf2       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ccdf2                bootloader_read_bootloader_header
 .text.bootloader_check_bootloader_validity
                0x403cce30       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403cce30                bootloader_check_bootloader_validity
 .text.bootloader_config_wdt
                0x403cceb4       0x9c esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403cceb4                bootloader_config_wdt
 .text.bootloader_enable_random
                0x403ccf50       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ccf50                bootloader_enable_random
 .text.bootloader_print_banner
                0x403ccf78       0x56 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ccf78                bootloader_print_banner
 .text.bootloader_console_init
                0x403ccfce      0x1a4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                0x403ccfce                bootloader_console_init
 .text.esp_cpu_configure_region_protection
                0x403cd172      0x100 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                0x403cd172                esp_cpu_configure_region_protection
 .text.rtc_clk_init
                0x403cd272      0x152 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                0x403cd272                rtc_clk_init
 *(.iram .iram.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x403cd3d4                        . = (. + 0x10)
 *fill*         0x403cd3c4       0x10 
                0x403cd3d4                        _text_end = ABSOLUTE (.)
                0x403cd3d4                        _etext = .

.riscv.attributes
                0x00000000       0x48
 *(.riscv.attributes)
 .riscv.attributes
                0x00000000       0x44 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .riscv.attributes
                0x00000044       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .riscv.attributes
                0x00000088       0x44 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .riscv.attributes
                0x000000cc       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .riscv.attributes
                0x00000110       0x44 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .riscv.attributes
                0x00000154       0x44 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .riscv.attributes
                0x00000198       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .riscv.attributes
                0x000001dc       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .riscv.attributes
                0x00000220       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .riscv.attributes
                0x00000264       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .riscv.attributes
                0x000002a8       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .riscv.attributes
                0x000002ec       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .riscv.attributes
                0x00000330       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .riscv.attributes
                0x00000374       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .riscv.attributes
                0x000003bc       0x44 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .riscv.attributes
                0x00000400       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .riscv.attributes
                0x00000444       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .riscv.attributes
                0x00000488       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .riscv.attributes
                0x000004cc       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .riscv.attributes
                0x00000510       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .riscv.attributes
                0x00000554       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .riscv.attributes
                0x0000059c       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .riscv.attributes
                0x000005e4       0x44 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .riscv.attributes
                0x00000628       0x44 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .riscv.attributes
                0x0000066c       0x44 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .riscv.attributes
                0x000006b0       0x48 esp-idf/log/liblog.a(log_noos.c.obj)
 .riscv.attributes
                0x000006f8       0x44 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .riscv.attributes
                0x0000073c       0x44 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x00000780       0x44 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x000007c4       0x44 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .riscv.attributes
                0x00000808       0x44 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .riscv.attributes
                0x0000084c       0x44 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug
 *(.debug)

.line
 *(.line)

.debug_srcinfo
 *(.debug_srcinfo)

.debug_sfnames
 *(.debug_sfnames)

.debug_aranges  0x00000000      0x8a8
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_aranges
                0x00000020       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_aranges
                0x00000048       0x28 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_aranges
                0x00000070       0x98 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_aranges
                0x00000108       0x20 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_aranges
                0x00000128       0x70 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_aranges
                0x00000198       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_aranges
                0x000001b8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_aranges
                0x000001e8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_aranges
                0x00000218       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_aranges
                0x00000238       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_aranges
                0x00000288       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_aranges
                0x000002a8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_aranges
                0x000002c8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_aranges
                0x000002e8       0x40 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_aranges
                0x00000328       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_aranges
                0x00000350       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_aranges
                0x00000408       0x58 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_aranges
                0x00000460       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_aranges
                0x000004a8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_aranges
                0x000004c8       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_aranges
                0x000004e8       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_aranges
                0x00000508      0x120 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_aranges
                0x00000628       0x28 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_aranges
                0x00000650       0x28 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_aranges
                0x00000678       0x38 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_aranges
                0x000006b0       0x18 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .debug_aranges
                0x000006c8       0x48 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x00000710       0x58 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x00000768       0x70 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_aranges
                0x000007d8       0x60 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_aranges
                0x00000838       0x70 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_pubnames
 *(.debug_pubnames)

.debug_info     0x00000000    0x2c849
 *(.debug_info .gnu.linkonce.wi.*)
 .debug_info    0x00000000      0x181 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_info    0x00000181      0x21d esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_info    0x0000039e      0xc95 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_info    0x00001033     0x1f19 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_info    0x00002f4c      0x5ae esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_info    0x000034fa     0x267c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_info    0x00005b76       0xc6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_info    0x00005c3c      0x437 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_info    0x00006073      0x104 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_info    0x00006177      0x845 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_info    0x000069bc      0xa98 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_info    0x00007454      0x349 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_info    0x0000779d       0x9f esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_info    0x0000783c      0x26b esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_info    0x00007aa7      0x987 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_info    0x0000842e      0x1d8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_info    0x00008606     0x36d2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_info    0x0000bcd8      0xc2f esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_info    0x0000c907     0x46cb esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_info    0x00010fd2     0x7ff1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_info    0x00018fc3      0x26b esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_info    0x0001922e      0x8d1 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_info    0x00019aff     0x1796 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_info    0x0001b295      0x160 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_info    0x0001b3f5      0x413 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_info    0x0001b808      0x281 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_info    0x0001ba89      0x32e esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .debug_info    0x0001bdb7     0x378d esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x0001f544     0x3a05 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x00022f49     0x819c esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_info    0x0002b0e5      0xbd2 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_info    0x0002bcb7      0xb92 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_abbrev   0x00000000     0x4b7e
 *(.debug_abbrev)
 .debug_abbrev  0x00000000       0xaa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_abbrev  0x000000aa       0xfb esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_abbrev  0x000001a5      0x309 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_abbrev  0x000004ae      0x507 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_abbrev  0x000009b5      0x1fd esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_abbrev  0x00000bb2      0x51d esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_abbrev  0x000010cf       0x89 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_abbrev  0x00001158      0x1ca esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_abbrev  0x00001322       0x80 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_abbrev  0x000013a2      0x1fe esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_abbrev  0x000015a0      0x296 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_abbrev  0x00001836      0x12e esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_abbrev  0x00001964       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_abbrev  0x000019c9      0x16d esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_abbrev  0x00001b36      0x322 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_abbrev  0x00001e58       0xaf esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_abbrev  0x00001f07      0x568 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_abbrev  0x0000246f      0x30e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_abbrev  0x0000277d      0x344 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_abbrev  0x00002ac1      0x3c5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_abbrev  0x00002e86       0x90 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_abbrev  0x00002f16      0x306 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_abbrev  0x0000321c      0x525 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_abbrev  0x00003741       0xd3 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_abbrev  0x00003814      0x13a esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_abbrev  0x0000394e      0x1a5 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_abbrev  0x00003af3       0xd4 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .debug_abbrev  0x00003bc7      0x1ff esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00003dc6      0x35d esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00004123      0x3fc esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_abbrev  0x0000451f      0x2f8 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_abbrev  0x00004817      0x367 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_line     0x00000000    0x121d6
 *(.debug_line)
 .debug_line    0x00000000      0x1e2 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_line    0x000001e2      0x3af esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_line    0x00000591      0x5c4 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_line    0x00000b55     0x1e0a esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_line    0x0000295f      0x65c esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_line    0x00002fbb     0x221e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_line    0x000051d9      0x1c0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_line    0x00005399      0x42f esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_line    0x000057c8      0x2bf esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_line    0x00005a87      0x82f esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_line    0x000062b6      0x92a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_line    0x00006be0      0x3bd esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_line    0x00006f9d       0xcd esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_line    0x0000706a      0x487 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_line    0x000074f1      0xb85 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_line    0x00008076      0x653 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_line    0x000086c9     0x197d esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_line    0x0000a046      0xb7a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_line    0x0000abc0      0x9fd esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_line    0x0000b5bd      0x884 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_line    0x0000be41      0x3b6 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_line    0x0000c1f7      0x726 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_line    0x0000c91d     0x1be2 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_line    0x0000e4ff      0x108 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_line    0x0000e607      0x2e3 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_line    0x0000e8ea      0x3e4 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_line    0x0000ecce      0x1d6 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .debug_line    0x0000eea4      0x447 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0000f2eb      0x72d esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0000fa18     0x12ff esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_line    0x00010d17      0xbc3 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_line    0x000118da      0x8fc esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_frame    0x00000000     0x1640
 *(.debug_frame)
 .debug_frame   0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_frame   0x00000020       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_frame   0x00000060       0x38 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_frame   0x00000098      0x288 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_frame   0x00000320       0x58 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_frame   0x00000378      0x198 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_frame   0x00000510       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_frame   0x00000530       0x68 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_frame   0x00000598       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_frame   0x000005d8       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_frame   0x0000060c      0x100 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_frame   0x0000070c       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_frame   0x0000073c       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_frame   0x0000075c       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_frame   0x00000788       0xc0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_frame   0x00000848       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_frame   0x00000898      0x2b8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_frame   0x00000b50      0x100 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_frame   0x00000c50       0xc4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_frame   0x00000d14       0x3c esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_frame   0x00000d50       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_frame   0x00000d70       0x38 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_frame   0x00000da8      0x33c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_frame   0x000010e4       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_frame   0x00001114       0x50 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_frame   0x00001164       0x84 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_frame   0x000011e8       0x80 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x00001268       0xb0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x00001318       0xec esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_frame   0x00001404      0x11c esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_frame   0x00001520      0x120 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_str      0x00000000     0xf6d8
 *(.debug_str)
 .debug_str     0x00000000     0xf6d8 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                0x2c4 (size before relaxing)
 .debug_str     0x0000f6d8      0x2da esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_str     0x0000f6d8      0xa60 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_str     0x0000f6d8     0x169b esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_str     0x0000f6d8      0x4b3 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_str     0x0000f6d8     0x185c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_str     0x0000f6d8      0x268 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_str     0x0000f6d8      0x3f5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_str     0x0000f6d8      0x2b1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_str     0x0000f6d8     0x1105 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_str     0x0000f6d8      0xedc esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_str     0x0000f6d8      0x6d1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_str     0x0000f6d8      0x251 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_str     0x0000f6d8      0x2ec esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_str     0x0000f6d8      0x9c9 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_str     0x0000f6d8      0x297 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_str     0x0000f6d8     0x2721 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_str     0x0000f6d8     0x1365 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_str     0x0000f6d8     0x359b esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_str     0x0000f6d8     0x4f57 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_str     0x0000f6d8      0x251 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_str     0x0000f6d8      0xcbe esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_str     0x0000f6d8     0x135a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_str     0x0000f6d8      0x2a4 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_str     0x0000f6d8      0x575 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_str     0x0000f6d8      0x2fa esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_str     0x0000f6d8      0x950 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .debug_str     0x0000f6d8     0x2826 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x0000f6d8     0x29e5 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x0000f6d8     0x5141 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_str     0x0000f6d8      0x614 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_str     0x0000f6d8      0x617 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_loc      0x00000000     0x8d53
 *(.debug_loc)
 .debug_loc     0x00000000       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_loc     0x000000c8       0xc7 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_loc     0x0000018f     0x1248 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_loc     0x000013d7      0x1d9 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_loc     0x000015b0     0x24a4 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_loc     0x00003a54      0x1bb esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_loc     0x00003c0f       0x69 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_loc     0x00003c78      0x3e0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_loc     0x00004058      0x1b5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_loc     0x0000420d       0xf8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_loc     0x00004305      0x17c esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_loc     0x00004481     0x1184 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_loc     0x00005605      0x2d1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_loc     0x000058d6      0x157 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_loc     0x00005a2d      0x179 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_loc     0x00005ba6       0xf2 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_loc     0x00005c98      0xcaf esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_loc     0x00006947       0x97 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_loc     0x000069de       0x8e esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_loc     0x00006a6c      0x1a0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_loc     0x00006c0c      0xac7 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_loc     0x000076d3      0xc52 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_loc     0x00008325      0xa2e esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_macinfo
 *(.debug_macinfo)

.debug_pubtypes
 *(.debug_pubtypes)

.debug_ranges   0x00000000     0x1820
 *(.debug_ranges)
 .debug_ranges  0x00000000       0x10 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_ranges  0x00000010       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_ranges  0x00000028       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_ranges  0x00000058      0x200 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_ranges  0x00000258       0x70 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_ranges  0x000002c8      0x438 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_ranges  0x00000700       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_ranges  0x00000710       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_ranges  0x00000730       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_ranges  0x00000750       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .debug_ranges  0x00000790       0x78 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_ranges  0x00000808       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_ranges  0x00000818       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_ranges  0x00000828       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_ranges  0x00000850       0x90 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_ranges  0x000008e0       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .debug_ranges  0x000008f8      0x180 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_ranges  0x00000a78       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .debug_ranges  0x00000b30       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_ranges  0x00000ba0       0xf8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_ranges  0x00000c98       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_ranges  0x00000ca8       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_ranges  0x00000ce8      0x3d0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_ranges  0x000010b8       0x18 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .debug_ranges  0x000010d0       0x18 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .debug_ranges  0x000010e8       0x60 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_ranges  0x00001148       0x88 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x000011d0       0x98 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x00001268      0x2c8 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .debug_ranges  0x00001530      0x180 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_ranges  0x000016b0      0x170 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_weaknames
 *(.debug_weaknames)

.debug_funcnames
 *(.debug_funcnames)

.debug_typenames
 *(.debug_typenames)

.debug_varnames
 *(.debug_varnames)

.debug_gnu_pubnames
 *(.debug_gnu_pubnames)

.debug_gnu_pubtypes
 *(.debug_gnu_pubtypes)

.debug_types
 *(.debug_types)

.debug_addr
 *(.debug_addr)

.debug_line_str
 *(.debug_line_str)

.debug_loclists
 *(.debug_loclists)

.debug_macro
 *(.debug_macro)

.debug_names
 *(.debug_names)

.debug_rnglists
 *(.debug_rnglists)

.debug_str_offsets
 *(.debug_str_offsets)

.comment        0x00000000       0x2f
 *(.comment)
 .comment       0x00000000       0x2f esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                 0x30 (size before relaxing)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .comment       0x0000002f       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
 .comment       0x0000002f       0x30 esp-idf/log/liblog.a(log_noos.c.obj)
 .comment       0x0000002f       0x30 esp-idf/soc/libsoc.a(uart_periph.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(cache_hal.c.obj)

.note.GNU-stack
 *(.note.GNU-stack)

/DISCARD/
 *(.rela.*)
OUTPUT(bootloader.elf elf32-littleriscv)

Cross Reference Table

Symbol                                            File
Cache_Disable_ICache                              esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_ICache                               esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Get_ICache_Line_Size                        esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Invalidate_Addr                             esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Resume_ICache                               esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_ICache                              esp-idf/hal/libhal.a(cache_hal.c.obj)
EFUSE                                             esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN0                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN1                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN2                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN3                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN1                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN2                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN3                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MAJOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MINOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIG_DBIAS_HVT                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_DIRECT_BOOT                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_ICACHE                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MODE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_DIS_FORCE_DOWNLOAD                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_ICACHE                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_PAD_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_TWAI                                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_ERR_RST_ENABLE                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_CAP                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_TEMP                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_TPUW                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_VENDOR                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FORCE_SEND_RESUME                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_JTAG_SEL_ENABLE                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KEY0                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY1                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY2                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY3                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY4                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY5                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_0                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_1                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_2                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_3                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_4                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_5                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_K_DIG_LDO                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_K_RTC_LDO                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_MAC                                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_OCODE                                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_OPTIONAL_UNIQUE_ID                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PKG_VERSION                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_RD_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY1                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY2                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_EN                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_VERSION                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SOFT_DIS_JTAG                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_BOOT_CRYPT_CNT                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_CLK                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_CS                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_D                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_D4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_D5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_D6                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_D7                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_DQS                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_HD                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_Q                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_PAD_CONFIG_WP                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SYS_DATA_PART2                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_TEMP_CALIB                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_THRES_HVT                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_UART_PRINT_CONTROL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_USB_EXCHG_PINS                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA_MAC_CUSTOM                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_VDD_SPI_AS_GPIO                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_V_DIG_DBIAS20                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_V_RTC_DBIAS20                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MAJOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MINOR_HI                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MINOR_LO                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WDT_DELAY_SEL                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN1              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN2              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN1            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN2            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK1                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY1                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY2                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_USR_DATA                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_CUSTOM_MAC                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIG_DBIAS_HVT                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_ICACHE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_WR_DIS_DIS_PAD_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_TWAI                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ERR_RST_ENABLE                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_CAP                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TEMP                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TPUW                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_VENDOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_0                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_1                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_2                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_3                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_4                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_5                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_K_DIG_LDO                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_K_RTC_LDO                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_MAC                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_OCODE                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PKG_VERSION                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RD_DIS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_EN                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_VERSION                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_CLK               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_CS                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D4                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D5                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D6                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_D7                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_DQS               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_HD                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_Q                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_PAD_CONFIG_WP                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SYS_DATA_PART1                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_TEMP_CALIB                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_THRES_HVT                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_USB_EXCHG_PINS                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_VDD_SPI_AS_GPIO                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_V_DIG_DBIAS20                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_V_RTC_DBIAS20                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR_HI           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR_LO           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WDT_DELAY_SEL                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
GetUartDevice                                     esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
ROM_Boot_Cache_Init                               esp-idf/hal/libhal.a(mmu_hal.c.obj)
RTCCNTL                                           esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
SPIMEM0                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
SPIMEM1                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
SYSTEM                                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
TIMERG0                                           esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
TIMERG1                                           esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
UART1                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
__ashldi3                                         E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__assert_func                                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/log/liblog.a(log_noos.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
__clz_tab                                         E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                                                  E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
__divdi3                                          E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
__getreent                                        esp-idf/main/libmain.a(bootloader_start.c.obj)
__lshrdi3                                         E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__popcountsi2                                     E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__sf                                              E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
__udivdi3                                         E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
_bss_end                                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
_bss_start                                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
_data_end                                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
_data_start                                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
_dram_end                                         esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_dram_start                                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_impure_data                                      E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
_impure_ptr                                       E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
_loader_text_end                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_loader_text_start                                esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
abort                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_after_init                             esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_ana_bod_reset_config                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_ana_clock_glitch_reset_config          esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_ana_super_wdt_reset_config             esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_atexit                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_before_init                            esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_check_bootloader_validity              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_clear_bss_section                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_clock_configure                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_common_check_chip_validity             esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_check_efuse_blk_validity        esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_get_active_otadata              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_get_partition_description       esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_crc                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_invalid              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_valid                esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_common_read_otadata                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_select_otadata                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_config_wdt                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_configure_spi_pins                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_console_deinit                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_console_init                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_debug_buffer                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_enable_random                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_enable_wp                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_execute_flash_command                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_fill_random                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_clock_config                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_cs_timing_config                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_dummy_config                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_erase_range                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_execute_command_common           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_get_spi_mode                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_is_octal_mode_enabled            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_read_sfdp                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_reset_chip                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_set_dummy_out                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_unlock                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_update_id                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_flash_update_size                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_flash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_xmc_startup                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_image_hdr                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_init                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_init_mem                               esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_init_spi_flash                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_load_image                             esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_load_image_no_verify                   esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_mmap                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_mmap_get_free_pages                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_munmap                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_print_banner                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_random_disable                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_random_enable                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
bootloader_read_bootloader_header                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
bootloader_read_flash_id                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
bootloader_reset                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_sha256_data                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_finish                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_flash_contents                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_hex_to_str                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_start                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_spi_flash_reset                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_utility_get_selected_boot_partition    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_boot_image                esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_partition_table           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
cache_hal_disable                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_enable                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_get_cache_line_size                     esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_init                                    esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
cache_hal_invalidate_addr                         esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_is_cache_enabled                        esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_resume                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_suspend                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_vaddr_to_cache_level_id                 esp-idf/hal/libhal.a(cache_hal.c.obj)
call_start_cpu0                                   esp-idf/main/libmain.a(bootloader_start.c.obj)
efuse_hal_blk_version                             esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_chip_revision                           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
efuse_hal_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_flash_encryption_enabled                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
efuse_hal_get_disable_blk_version_major           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_disable_wafer_version_major         esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_mac                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_major_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_minor_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_is_coding_error_in_block                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_program                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_read                                    esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_set_timing                              esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_bootloader_desc                               esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
esp_bootloader_get_description                    esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
esp_cpu_configure_region_protection               esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
esp_efuse_batch_write_begin                       esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_batch_write_cancel                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_batch_write_commit                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_block_is_empty                          esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_check_errors                            esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_count_unused_key_blocks                 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_destroy_block                           esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_disable_rom_download_mode               esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_enable_rom_secure_download_mode         esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_purpose                            esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_unused_key_block                   esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_coding_scheme                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_digest_revoke                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_field_size                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_key                                 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_pkg_ver                             esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_get_purpose_field                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_write_protect_of_digest_revoke      esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_key_block_unused                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_block                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_field_bit                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_blob                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_cnt                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_reg                                esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_digest_revoke                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_read_protect                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_rom_log_scheme                      esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_set_write_protect                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_write_protect_of_digest_revoke      esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_apply_new_coding_scheme         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_chip                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_burn_chip_opt                   esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_efuses                     esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_check_errors                    esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_clear_program_registers         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_count_once                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_debug_dump_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_pending              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_single_block         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_erase_virt_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_fill_buff                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_number_of_items             esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_read_register_address       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_is_correct_written_data         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_process                         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_read_reg                        esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_reset                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_update_virt_blocks              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_write_blob                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_cnt                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_reg                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_block                             esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_field_bit                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_field_blob                        esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_write_field_cnt                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_key                               esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_keys                              esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_reg                               esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_flash_encryption_cfg_verify_release_mode      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_encryption_enabled                      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_flash_encryption_set_release_mode             esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_write_protect_crypt_cnt                 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_get_flash_encryption_mode                     esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_image_get_flash_size                          esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_get_metadata                            esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader_data                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_log_early_timestamp                           esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_lock                                 esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_lock_timeout                         esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_unlock                               esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_timestamp                                 esp-idf/log/liblog.a(log_noos.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_partition_table_verify                        esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_crc32_le                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
esp_rom_delay_us                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_efuse_get_flash_gpio_info                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
esp_rom_efuse_get_flash_wp_gpio                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
esp_rom_get_cpu_ticks_per_us                      esp-idf/log/liblog.a(log_noos.c.obj)
esp_rom_get_reset_reason                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_rom_gpio_connect_in_signal                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_gpio_connect_out_signal                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_gpio_pad_pullup_only                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_gpio_pad_set_drv                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
esp_rom_install_channel_putc                      esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
esp_rom_install_uart_printf                       esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_md5_final                                 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_md5_init                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_md5_update                                esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_output_flush_tx                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
esp_rom_output_set_as_console                     esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_output_switch_buffer                      esp-idf/esp_rom/libesp_rom.a(esp_rom_uart.c.obj)
esp_rom_output_tx_wait_idle                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
esp_rom_printf                                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
esp_rom_regi2c_write                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_regi2c_write_mask                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
esp_rom_set_cpu_ticks_per_us                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_software_reset_system                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_spiflash_config_clk                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
esp_rom_spiflash_config_param                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
esp_rom_spiflash_erase_block                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_wait_idle                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write_encrypted                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_secure_boot_read_key_digests                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ets_efuse_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_efuse_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_install_putc1                                 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
ets_install_putc2                                 esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
ets_install_uart_printf                           esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
ets_sha_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_finish                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_init                                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_update                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
g_uart_print                                      esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
g_usb_print                                       esp-idf/esp_rom/libesp_rom.a(esp_rom_sys.c.obj)
memcmp                                            E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
memcpy                                            E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
memset                                            E:/esp_tool/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                                                  esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
mmu_hal_bytes_to_pages                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_check_valid_ext_vaddr_region              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_init                                      esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c3.c.obj)
mmu_hal_map_region                                esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_paddr_to_vaddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_pages_to_bytes                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_unmap_all                                 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_unmap_region                              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_vaddr_to_paddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
range_read_addr_blocks                            esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
range_write_addr_blocks                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
rom_spiflash_legacy_data                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c3.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
rtc_clk_32k_bootstrap                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_32k_enable                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_32k_enable_external                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_32k_enabled                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_8m_divider_set                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_8m_enable                                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_8md256_enabled                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_apb_freq_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_apb_freq_update                           esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_bbpll_add_consumer                        esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_bbpll_remove_consumer                     esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_get_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_mhz_to_config                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config_fast                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_set_xtal                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_set_to_default_config                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_divider_set                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_fast_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_fast_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_init                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_slow_freq_get_hz                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
rtc_clk_slow_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_slow_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_xtal_freq_get                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
rtc_clk_xtal_freq_update                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_dig_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_disable                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_enable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_get_xtal                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
s_cache_hal_init_ctx                              esp-idf/hal/libhal.a(cache_hal.c.obj)
s_revoke_table                                    esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
s_table                                           esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
uart_periph_signal                                esp-idf/soc/libsoc.a(uart_periph.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
wdt_hal_config_stage                              esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_deinit                                    esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
wdt_hal_disable                                   esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
wdt_hal_enable                                    esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_feed                                      esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
wdt_hal_handle_intr                               esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
wdt_hal_init                                      esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_is_enabled                                esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
wdt_hal_set_flashboot_en                          esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_disable                     esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_enable                      esp-idf/hal/libhal.a(wdt_hal_iram.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
